import time
import logging
from typing import List, Dict
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

import requests
from django.utils import timezone
from django.db.models import QuerySet
from django.core.management.base import BaseCommand

from mainapp.models import WebsiteScanQueue, Website
from mainapp.website_scanning import WebsiteScanning
from AbunDRFBackend.settings import FLY_API_HOST, FLY_WEBSITE_SCANNING_APP_NAME, FLY_WEBSITE_SCANNING_DEPLOY_TOKEN

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Process queued website scanning tasks'
    DELAY = 60
    TOTAL_MACHINES_RESERVED = 150
    URLS_PER_MACHINE = 50
    MAX_RETRIES = 5

    def handle(self, *args, **kwargs):
        self.stdout.write('Starting website scan queue processing...')

        try:
            self.process_queue()
        except Exception as e:
            # Check if it's a URL validation error and handle it gracefully
            error_message = str(e)
            if "is not a HTTP(s) URL" in error_message or "URLField" in error_message:
                logger.warning(f"URL validation error in scan queue processing: {error_message}")
            else:
                logger.critical(f"Error processing scan queue: {error_message}")
            self.stdout.write(self.style.ERROR(f'Error processing scan queue: {error_message}'))

    def get_running_machines(self) -> List[Dict]:
        """Get current running machines from Fly.io"""
        try:
            response = requests.get(
                f"{FLY_API_HOST}/apps/{FLY_WEBSITE_SCANNING_APP_NAME}/machines",
                headers={
                    'Authorization': f"Bearer {FLY_WEBSITE_SCANNING_DEPLOY_TOKEN}",
                    'Content-Type': 'application/json'
                },
                timeout=30
            )
            
            if response.status_code == 200:
                machines = response.json()
                return [m for m in machines if m['state'] == 'started']
            else:
                logger.error(f"Failed to get machines: {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting machine list: {e}")
            return []

    def pre_fetch_website_sitemaps_and_pages(self, task: WebsiteScanQueue) -> WebsiteScanning | None:
        """
        Pre-Fetch the website sitemaps & pages
        """
        website: Website = task.website

        if website.is_crawling:
            logger.warning(f"Website scanning task is already processing for {website.domain}. Skipping...")
            task.status = "failed"
            task.error_message = "Website scanning tasks is already in processing."
            task.save()
            return None

        # Start processing the website scanning task
        task.status = 'processing'
        task.started_at = timezone.now()
        task.save()

        try:
            # Initialize WebsiteScanning instance
            websiteScanning = WebsiteScanning(website=website,
                                              sitemaps=task.sitemap_urls,
                                              website_urls=task.website_urls,
                                              urls_to_process_per_machine=self.URLS_PER_MACHINE,
                                              website_scan_queue=task,
                                              tool_to_run=task.tool_to_run)

            # Fetch the website sitemaps & pages
            websiteScanning.fetch_website_sitemaps_and_urls()

            self.stdout.write(
                self.style.SUCCESS(
                    f'Website sitemaps & pages fetched for {website.domain}'
                )
            )

            return websiteScanning

        except TimeoutError as err:
            logger.error(err)
            logger.warning(f"Failed to fetch the sitemaps for website: {website.domain}. Marking task as failed...")

            # Mark the task as failed
            task.status = "failed"
            task.error_message = "Failed to fetch the sitemaps"
            task.save()

        return None

    def process_queue(self) -> None:
        """
        Process the website scan queue in batches
        """
        while True:
            # Get batch of queued tasks
            queued_tasks: QuerySet[WebsiteScanQueue] = WebsiteScanQueue.objects.filter(status='queued')

            if not queued_tasks:
                # If no tasks are queued, wait for a specified delay before checking again
                self.stdout.write(f'No queued tasks found. Waiting for {self.DELAY} seconds...')
                time.sleep(self.DELAY)
                continue

            with ThreadPoolExecutor(max_workers=5) as executor:
                # Parallely fetch the website sitemaps and urls
                future_to_task = {executor.submit(self.pre_fetch_website_sitemaps_and_pages, task): task for task in queued_tasks}

                for future in as_completed(future_to_task):
                    websiteScanning = future.result()

                    if not websiteScanning:
                        # If pre-fetching fails, skip to the next task
                        continue

                    # Get the website scan queue and website instance
                    task = websiteScanning.website_scan_queue
                    website = websiteScanning.website

                    # Attempt to process each task, retrying up to MAX_RETRIES times
                    for _ in range(self.MAX_RETRIES):
                        try:
                            self.process_single_task(websiteScanning)
                            self.stdout.write(
                                self.style.SUCCESS(
                                    f'Successfully processed task for website: {task.website.domain}'
                                )
                            )
                            break
                        
                        except Exception as e:
                            # Log and report any errors encountered during processing
                            error_message = str(e)

                            # Handle URL validation errors more gracefully
                            if "is not a HTTP(s) URL" in error_message or "URLField" in error_message:
                                logger.warning(f"URL validation error processing task {task.id}: {error_message}")
                                # Mark task as failed due to invalid URLs
                                task.status = "failed"
                                task.error_message = "Invalid URLs found in sitemap data"
                                task.save()
                                break  # Don't retry for URL validation errors
                            else:
                                logger.error(f"Error processing task {task.id}: {error_message}")

                            self.stdout.write(
                                self.style.ERROR(
                                    f'Error processing task for website {task.website.domain}: {error_message}'
                                )
                            )

                            self.stdout.write(
                                self.style.SUCCESS(
                                    f'Will Retry after {self.DELAY}...'
                                )
                            )

                            time.sleep(self.DELAY)

                    else:
                        # If all retries fail, mark the task and website as failed
                        task.status = 'failed'
                        task.error_message = error_message
                        task.save()

                        website.is_crawled = False
                        website.is_crawling = False
                        website.is_failed = True
                        website.task_queued = False
                        website.save()

    def process_single_task(self, websiteScanning: WebsiteScanning) -> None:
        """
        Process a single website scanning task
        """
        # Calculate the number of available machines
        available_machines = max(0, self.TOTAL_MACHINES_RESERVED - len(self.get_running_machines()))

        while available_machines <= 0:
            # If no machines are available, wait and check again
            logger.info(f"No machine is available to run the task. Waiting for {self.DELAY} seconds...")
            time.sleep(self.DELAY)

            # Update the available machines
            available_machines = max(0, self.TOTAL_MACHINES_RESERVED - len(self.get_running_machines()))

        # Set the available machines
        websiteScanning.available_machines = available_machines

        # Start the website scraping process
        websiteScanning.run()

        while websiteScanning.remaining_urls_to_process_per_machine:
            # Check for available machines to continue processing remaining URLs
            available_machines = max(0, self.TOTAL_MACHINES_RESERVED - len(self.get_running_machines()))

            if available_machines <= 0:
                # If no machines are available, wait and check again
                logger.info(f"No machine is available to process the remaining URLs. Waiting for {self.DELAY} seconds...")
                time.sleep(self.DELAY)
                continue

            # Update the available machines for continued processing
            websiteScanning.available_machines = available_machines

            try:
                # Continue processing the remaining URLs
                websiteScanning.continue_processing()
            except RuntimeError as err:
                # Log any runtime errors encountered during processing
                logger.error(err)
                break
