"""

====================================================================================================================
This script will be run periodcally using cronjob to fetch and store all the indexation details for a website.
====================================================================================================================
"""
import itertools
from typing import Dict, List, Tuple
from concurrent.futures import ThreadPoolExecutor
from urllib3.exceptions import SSLError, MaxRetryError, ConnectionError

from django.core.management import BaseCommand
from django.db.models import QuerySet
from django.db.models import Exists, OuterRef

from google.oauth2.credentials import Credentials
from google.auth.exceptions import RefreshError
from googleapiclient.errors import HttpError

from mainapp.models import User, WebsiteIndexation, Website, Article, GoogleIntegration
from mainapp.quickindex import sitemaps as fetch_sitemaps
from mainapp.email_messages import website_indexation_process_completed_body
from mainapp.utils import send_email
from AbunDRFBackend.settings import logging, ABUN_NOTIFICATION_EMAIL
from mainapp.google_integration_utils import (get_google_oauth2_credentials, get_site_name_on_gsc,
                                              fetch_sitemap_information, fetch_url_inspection_details)


logger = logging.getLogger(__file__)


class Command(BaseCommand):
    QPD = 2000  # queries per day for URL inspection (calls querying the same site)
    QPM = 600   # queries per minute for URL inspection (calls querying the same site)

    @staticmethod
    def all_indexed_pages_stored(indexation_details: List[Dict] | None) -> Tuple[bool, int]:
        """
        Checks if all pages are indexed
        :param indexation_details: Indexation data
        """
        if not indexation_details:
            return True, 0

        for idx, page in enumerate(indexation_details):
            if page.get('indexed') is None:
                return False, idx

        return True, 0

    @staticmethod
    def all_indexed_pages_updated(indexation_details: List[Dict] | None) -> Tuple[bool, int]:
        """
        Checks if all pages are updated
        :param indexation_details: Indexation data
        """
        if not indexation_details or indexation_details[0].get('updated') is None:
            return True, 0

        for idx, page in enumerate(indexation_details):
            updated: bool | None = page.get('updated')
            if not updated:
                return False, idx

        return True, 0

    def fetch_website_urls(self, credentials: Credentials, site: str) -> List:
        """
        Fetches all the URLs of the website via sitemap.
        Uses Search Console sitemaps if available; falls back to default sitemap.xml.
        :param credentials: Google oauth2 credentials
        :param site: site name on GSC (e.g., 'sc-domain:creatiwise.com')
        """

        self.stdout.write("[*] Fetching URLs\n")

        try:
            res = fetch_sitemap_information(credentials, site)
            sitemaps = res.get('sitemap', [])

        except HttpError as e:
            # Handle Google API errors for sitemap fetching
            self.handle_google_api_error(e, f"while fetching sitemap for {site}")
            sitemaps = []

        except Exception as e:
            logger.critical(f"Error fetching GSC sitemap for {site}: {e}")
            sitemaps = []

        all_urls = []

        if sitemaps:
            for sitemap in sitemaps:
                sitemap_url = sitemap.get('path')
                urls = fetch_sitemaps.fetch_urls_from_sitemap_recursive(sitemap_url)
                all_urls.extend(urls)
        else:
            logger.info("[*] No sitemaps found in GSC, trying default sitemap.xml\n")
            # Construct default sitemap.xml URL
            domain = site.replace("sc-domain:", "").replace("https://", "").replace("http://", "").strip("/")
            default_sitemap_url = f"https://{domain}/sitemap.xml"

            try:
                urls = fetch_sitemaps.fetch_urls_from_sitemap_recursive(default_sitemap_url)
                all_urls.extend(urls)

            except (MaxRetryError, ConnectionError) as e:
                # Handle connection errors specifically for sitemap fetching
                if "Failed to establish a new connection" in str(e) or "Name or service not known" in str(e):
                    logger.error(f"Connection failed while fetching sitemap for {domain}: {e}")
                else:
                    logger.error(f"Network error while fetching sitemap for {domain}: {e}")

            except Exception as e:
                logger.critical(f"Failed to fetch default sitemap for {domain}: {e}")

        # Deduplicate URLs
        unique_urls = list(set(all_urls))    
        return unique_urls


    def store_indexing_details(self, credentials: Credentials, site: str, website_pages: List,
                               website_indexation: WebsiteIndexation):
        """
        Stores indexing details to WebsiteIndexation model
        :param credentials: Google oauth2 credentials
        :param site: Connected website URL
        :param website_pages: All fetched pages
        :param website_indexation: WebsiteIndexation instance
        """
        self.stdout.write(f"[*] Storing index data start for '{website_indexation.website.domain}'\n")

        indexation_details: List[Dict] = []
        sliced_website_pages = website_pages[:self.QPD]
        total_pages = len(sliced_website_pages)
        executor = ThreadPoolExecutor(max_workers=min(max(1, self.QPM // 12), total_pages))

        if website_indexation.indexation_details:
            stored_indexation_details: List[Dict] = website_indexation.indexation_details
            all_pages = [page['url'] for page in stored_indexation_details]
        else:
            stored_indexation_details = None
            all_pages = []

        self.stdout.write(f"[*] Inspecting {total_pages} URLs.\n")

        for res, url in executor.map(fetch_url_inspection_details, itertools.repeat(credentials),
                                     itertools.repeat(site), sliced_website_pages):
            if not res:
                self.stdout.write(f"[X] Failed to inspect '{url}'\n")
                continue
            
            coverage = res['inspectionResult']['indexStatusResult'].get('coverageState')
            last_crawled = res['inspectionResult']['indexStatusResult'].get('lastCrawlTime', "")
            
            if coverage == "Submitted and indexed":
                indexation_details.append({
                    'url': url,
                    'indexed': True,
                    'sent_for_indexing': False,
                    'sent_on': "",
                    'coverage_state': coverage,
                    'last_crawled': last_crawled,
                    'status': "Index Completed"
                })
            else:
                if url in all_pages:
                    if coverage == "Crawled - currently not indexed" or coverage == "URL is unknown to Google":
                        indexation_details.append({
                            'url': url,
                            'indexed': False,
                            'sent_for_indexing': stored_indexation_details[all_pages.index(url)].get('sent_for_indexing', False),
                            'sent_on': stored_indexation_details[all_pages.index(url)].get('sent_on', ""),
                            'coverage_state': coverage,
                            'last_crawled': last_crawled,
                            'status': "Request Sent for Indexing" if stored_indexation_details[all_pages.index(url)].get('sent_for_indexing', False) else "Not Indexed"
                        })
                    else:
                        indexation_details.append({
                            'url': url,
                            'indexed': False,
                            'sent_for_indexing': stored_indexation_details[all_pages.index(url)].get('sent_for_indexing', False),
                            'sent_on': stored_indexation_details[all_pages.index(url)].get('sent_on', ""),
                            'coverage_state': coverage,
                            'last_crawled': last_crawled,
                            'status': "Rejected"
                        })
                else:
                    if coverage == "Crawled - currently not indexed" or coverage == "URL is unknown to Google":
                        indexation_details.append({
                            'url': url,
                            'indexed': False,
                            'sent_for_indexing': False,
                            'sent_on': "",
                            'coverage_state': coverage,
                            'last_crawled': last_crawled,
                            'status': "Not Indexed",
                        })
                    else: 
                        indexation_details.append({
                            'url': url,
                            'indexed': False,
                            'sent_for_indexing': False,
                            'sent_on': "",
                            'coverage_state': coverage,
                            'last_crawled': last_crawled,
                            'status': "Rejected",
                        })

        # store the remaining url
        sliced_website_pages = website_pages[self.QPD:]
        self.stdout.write(f"[*] Saving remaining {len(sliced_website_pages)} URLs\n")
        for page in sliced_website_pages:
            indexation_details.append({
                'url': page
            })

        # store the details in WebsiteIndexation
        website_indexation.indexation_details = indexation_details
        website_indexation.save()

    def continue_store_indexing_details(self, credentials: Credentials, site: str, website_pages: List,
                                        website_indexation: WebsiteIndexation, start_from: int):
        """
        Continue sotring the indexing details to WebsiteIndexation model
        :param credentials: Google oauth2 credentials
        :param site: Connected website URL
        :param website_pages: All fetched pages
        :param website_indexation: WebsiteIndexation instance
        :param start_from: Index to start from
        """
        self.stdout.write(f"[*] Storing index data continue for '{website_indexation.website.domain}'\n")

        indexation_details: List[Dict] = website_indexation.indexation_details
        sliced_website_pages = website_pages[start_from:(start_from + self.QPD)]
        total_pages = len(sliced_website_pages)
        executor = ThreadPoolExecutor(max_workers=min(max(1, self.QPM // 12), total_pages))

        self.stdout.write(f"[*] Inspecting {total_pages} URLs.\n")

        for i, (res, url) in enumerate(executor.map(fetch_url_inspection_details, itertools.repeat(credentials),
                                     itertools.repeat(site), sliced_website_pages)):
            
            actual_index = start_from + i
            
            if not res:
                self.stdout.write(f"[X] Failed to inspect '{url}'\n")                
                continue

            if res['inspectionResult']['indexStatusResult']['coverageState'] == "Submitted and indexed":
                indexation_details[actual_index]['url'] = url
                indexation_details[actual_index]['indexed'] = True
                indexation_details[actual_index]['sent_for_indexing'] = False
                indexation_details[actual_index]['coverage_state'] = res['inspectionResult']['indexStatusResult']['coverageState']
                indexation_details[actual_index]['status'] = "Index Completed"
            elif res['inspectionResult']['indexStatusResult']['coverageState'] in ["Crawled - currently not indexed", "URL is unknown to Google"]:
                indexation_details[actual_index]['url'] = url
                indexation_details[actual_index]['indexed'] = False
                indexation_details[actual_index]['coverage_state'] = res['inspectionResult']['indexStatusResult']['coverageState']
                indexation_details[actual_index]['status'] = "Request Sent for Indexing" if indexation_details[actual_index].get('sent_for_indexing', False) else "Not Indexed"
            else: 
                indexation_details[actual_index]['url'] = url
                indexation_details[actual_index]['indexed'] = False
                indexation_details[actual_index]['coverage_state'] = res['inspectionResult']['indexStatusResult']['coverageState']
                indexation_details[actual_index]['status'] = "Rejected"

            # start_from += 1

        website_indexation.indexation_details = indexation_details
        website_indexation.save()

    def update_indexing_details(self, credentials: Credentials, site: str, website_pages: List,
                                website_indexation: WebsiteIndexation):
        """
        Update the stored indexing details
        :param credentials: Google oauth2 credentials
        :param site: Connected website URL
        :param website_pages: All fetched pages
        :param website_indexation: WebsiteIndexation instance
        :param start_from: Index to start from
        """
        indexed_pages_details: List[Dict] = website_indexation.indexation_details
        indexed_page_urls: List = [page['url'] for page in indexed_pages_details]

        # Add new website pages
        for url in website_pages:
            if url not in indexed_page_urls:
                indexed_page_urls.append(url)
                indexed_pages_details.append({
                    'url': url,
                    'updated': False
                })

        all_pages_updated, start_from = self.all_indexed_pages_updated(indexed_pages_details)

        if all_pages_updated:
            self.stdout.write(f"[*] Updating index data for '{website_indexation.website.domain}'\n")
        else:
            self.stdout.write(f"[*] Updating remaining index data for '{website_indexation.website.domain}'\n")

        sliced_website_pages = indexed_page_urls[start_from:(start_from + self.QPD)]
        total_pages = len(sliced_website_pages)
        executor = ThreadPoolExecutor(max_workers=min(max(1, self.QPM // 12), total_pages))

        self.stdout.write(f"[*] Inspecting {total_pages} URLs.\n")

        for  i, (res, url) in enumerate(executor.map(fetch_url_inspection_details, itertools.repeat(credentials),
                                     itertools.repeat(site), sliced_website_pages)):
            
            actual_index = start_from + i
            
            if not res:
                self.stdout.write(f"[X] Failed to inspect '{url}'\n")
                continue

            if res['inspectionResult']['indexStatusResult']['coverageState'] == "Submitted and indexed":
                indexed_pages_details[actual_index]['indexed'] = True
                indexed_pages_details[actual_index]['updated'] = True
                indexed_pages_details[actual_index]['sent_for_indexing'] = False
                indexed_pages_details[actual_index]['coverage_state'] = res['inspectionResult']['indexStatusResult']['coverageState']
                indexed_pages_details[actual_index]['status'] = "Index Completed"
            elif res['inspectionResult']['indexStatusResult']['coverageState'] in ["Crawled - currently not indexed", "URL is unknown to Google"]:
                indexed_pages_details[actual_index]['indexed'] = False
                indexed_pages_details[actual_index]['updated'] = True
                indexed_pages_details[actual_index]['coverage_state'] = res['inspectionResult']['indexStatusResult']['coverageState']
                indexed_pages_details[actual_index]['status'] = "Request Sent for Indexing" if indexed_pages_details[actual_index].get('sent_for_indexing', False) else "Not Indexed"
            else:
                indexed_pages_details[actual_index]['indexed'] = False
                indexed_pages_details[actual_index]['updated'] = True
                indexed_pages_details[actual_index]['coverage_state'] = res['inspectionResult']['indexStatusResult']['coverageState']
                indexed_pages_details[actual_index]['status'] = "Rejected"

            # start_from += 1

        # store the details in WebsiteIndexation
        website_indexation.indexation_details = indexed_pages_details
        website_indexation.save()
        
    def normalize_url(self, url: str) -> str:
        return url.strip().rstrip("/").lower()

    def handle_google_api_error(self, error: HttpError, context: str = "") -> None:
        """
        Handle Google API errors with appropriate logging levels
        :param error: HttpError from Google API
        :param context: Additional context for the error
        """
        try:
            status_code = error.resp.status if hasattr(error, 'resp') and hasattr(error.resp, 'status') else None
            reason = error._get_reason() if hasattr(error, '_get_reason') else str(error)

            if status_code == 403:
                if "You do not own this site" in reason or "inspected URL is not part of this property" in reason:
                    logger.error(f"Google API 403 - Site ownership issue: {reason} {context}")

                elif "Quota exceeded" in reason:
                    logger.error(f"Google API 403 - Quota exceeded: {reason} {context}")

                else:
                    logger.error(f"Google API 403 - Forbidden: {reason} {context}")

            elif status_code == 429:
                logger.error(f"Google API 429 - Rate limit exceeded: {reason} {context}")

            elif status_code == 500:
                logger.error(f"Google API 500 - Internal server error: {reason} {context}")

            elif "invalid_grant" in reason.lower() or "bad request" in reason.lower():
                logger.error(f"Google API authentication error: {reason} {context}")

            else:
                logger.critical(f"Google API HttpError {status_code}: {reason} {context}")

        except Exception as e:
            logger.critical(f"Google API error (parsing failed): {error} {context} - Parse error: {e}")


    def handle(self, *args, **options):
        users = User.objects.filter(
            Exists(
                GoogleIntegration.objects.filter(website__user=OuterRef('pk'))
            ),
            admin=True # TODO: remove this line once testing is done.
        )

        for user in users:
            self.stdout.write(f"[*] Running for '{user.email}'\n")

            try:
                credentials: Credentials = get_google_oauth2_credentials(user, "google-search-console")

            except RefreshError as e:
                self.stdout.write("[X] Access has been revoked by the user or the token has expired. Skipping...\n")

                # Check for specific invalid_grant errors
                if "invalid_grant" in str(e).lower() or "bad request" in str(e).lower():
                    logger.error(f"Google OAuth invalid_grant error for user {user.email}: {e}")
                else:
                    logger.error(f"Google OAuth RefreshError for user {user.email}: {e}")

                continue

            except Exception as e:
                self.stdout.write(f"[X] Error while fetching google oauth2 credentials: {e}. Skipping...\n")

                # Handle other credential-related errors
                if any(keyword in str(e).lower() for keyword in ['invalid_grant', 'bad request', 'oauth', 'credential']):
                    logger.error(f"Google OAuth credential error for user {user.email}: {e}")
                else:
                    logger.critical(f"Unexpected credential error for user {user.email}: {e}")

                continue

            if not credentials:
                self.stdout.write("[X] Error while fetching google oauth2 credentials. Skipping...\n")
                continue

            self.stdout.write("[*] Google oauth2 credentials fetched!\n")

            websites: QuerySet[Website] = user.website_set.all()
            for website in websites:
                try:
                    site = get_site_name_on_gsc(credentials, website.domain)
                    website_indexation, _ = WebsiteIndexation.objects.get_or_create(user=user, website=website, search_engine='google')
                    all_data_stored, start_from = self.all_indexed_pages_stored(website_indexation.indexation_details)

                    # Update website indexation `urls_sent_for_indexing` count
                    website_indexation.urls_sent_for_indexing = 0
                    website_indexation.save()

                    if not site:
                        self.stdout.write(f"[X] Failed to fetch GSC site for '{website.domain}'. Skipping...\n")
                        continue
                    else:
                        self.stdout.write(f"[*] GSC site fetched for '{website.domain}'\n")

                    if not all_data_stored and not website_indexation.completed:
                        all_urls = [page['url'] for page in website_indexation.indexation_details]
                        self.continue_store_indexing_details(credentials, site, all_urls, website_indexation, start_from)
                    else:
                        # indexation_urls = [page['url'] for page in website_indexation.indexation_details]
                        sitemap_urls = self.fetch_website_urls(credentials, site)                                            
                                
                        
                        # Add Abun articles
                        posted_abun_articles = Article.objects.filter(website__user=user, is_posted=True).values_list('article_link', flat=True)
                        self.stdout.write(f"[*] {posted_abun_articles.count()} Abun articles URLs found.\n")
                        
                        all_urls = sitemap_urls + list(posted_abun_articles)
                        all_urls = [url for url in all_urls if url]
                        
                        seen = set()
                        deduped_urls = []
                        for url in all_urls:
                            norm = self.normalize_url(url)
                            if norm and norm not in seen:
                                seen.add(norm)
                                deduped_urls.append(url)
                        # all_urls.extend(posted_abun_articles)

                        if not deduped_urls:
                            self.stdout.write("[X] Failed to fetch the URLs from sitemap and Abun articles. Skipping...")
                            website_indexation.completed = True
                            website_indexation.indexation_details = []
                            website_indexation.save()
                            continue

                        if not website_indexation.completed:
                            self.store_indexing_details(credentials, site, deduped_urls, website_indexation)
                        else:
                            self.update_indexing_details(credentials, site, deduped_urls, website_indexation)

                    # Refresh from the database
                    website_indexation.refresh_from_db()

                    all_data_stored, _ = self.all_indexed_pages_stored(website_indexation.indexation_details)
                    if all_data_stored:
                        self.stdout.write("[*] Done. All pages index data stored!\n")

                        if not website_indexation.completed:
                            self.stdout.write(f"[*] Sending email notification for '{website.domain}'\n")
                            email_message = website_indexation_process_completed_body(user.username, website.domain)
                            send_email(
                                user.email,
                                ABUN_NOTIFICATION_EMAIL,
                                "Team Abun",
                                "Website Indexing Process Completed!",
                                email_message
                            )

                            website_indexation.completed = True
                            website_indexation.save()

                    else:
                        self.stdout.write(f"[*] Done. {self.QPD} pages index data stored!\n")

                except HttpError as err:
                    reason = err._get_reason() if hasattr(err, '_get_reason') else str(err)
                    self.stdout.write(f"[X] Google API HttpError -> '{reason}', Skipping...\n")
                    self.handle_google_api_error(err, f"for website {website.domain}")

                except RefreshError as err:
                    self.stdout.write(f"[X] Google OAuth RefreshError -> '{err}', Skipping...\n")
                    logger.error(f"Google OAuth RefreshError for website {website.domain}: {err}")

                except SSLError as err:
                    self.stdout.write(f"[X] SSLError -> '{err}', Skipping...\n")
                    logger.error(f"SSL connection error for website {website.domain}: {err}")

                except MaxRetryError as err:
                    self.stdout.write(f"[X] MaxRetryError -> '{err}', Skipping...\n")

                    if "Failed to establish a new connection" in str(err) or "Name or service not known" in str(err):
                        logger.error(f"Connection failed for website {website.domain}: {err}")

                    else:
                        logger.error(f"Max retries exceeded for website {website.domain}: {err}")

                except ConnectionError as err:
                    self.stdout.write(f"[X] ConnectionError -> '{err}', Skipping...\n")
                    logger.error(f"Connection error for website {website.domain}: {err}")

                except Exception as err:
                    self.stdout.write(f"[X] Unexpected error occurred! Skipping...\n")

                    if any(keyword in str(err).lower() for keyword in ['google', 'api', 'http', 'oauth', 'credential']):
                        logger.error(f"Google-related error for website {website.domain}: {err}")

                    else:
                        logger.critical(f"Unexpected error for website {website.domain}: {err}")
