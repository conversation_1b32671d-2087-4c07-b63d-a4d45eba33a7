"""
=====================================================================================================================================================
This `content_automation_cronjob` script is designed to publish automation projects based on specified criteria
such as the current day, time, user integrations, and publishing frequency.
It is intended to be run periodically using a cron job to find all `AutomationProject` objects that need to be 
published and then publish them using the user's current integration.

It first retrieves the current date and time in UTC and formats the current day as a lowercase string (e.g., 'monday').
It then fetches all `AutomationProject` objects that are either in the 'on' or 'draft' state and are scheduled to be published on the current day.

Next, the script iterates through these projects and uses a method to determine if each project should be published at the current time.
This method checks if the current time falls within the project's specified `auto_publish_time` range (e.g., 'morning', 'afternoon', etc.).
If the time is appropriate, it further checks if the user has the necessary integrations (e.g., WordPress, Webflow) and if the project has
not already been published according to its frequency (daily, weekly, or monthly).

If all these conditions are met, the project is added to a list of valid projects to be processed on the current run.
Finally, the script iterates through this list of valid projects to be processed and calls the `take_action_on_project` method for
each valid project to perform the actual publishing logic.
This method retrieves the user associated with the project, fetches the generated articles that are ready to be published,
filters out keywords that fall within the specified traffic range of the project, and publishes the articles using the user's integration (WordPress, Webflow, or Wix).

The `is_project_actionable_now` method in this script encapsulates the logic for determining whether a project should be published at the current time.
It checks the time range, user integrations, and the project's publishing frequency to ensure that only eligible projects are published.

=====================================================================================================================================================
"""
import time
import datetime
import logging
from zoneinfo import ZoneInfo
from typing import List

from django.core.management import BaseCommand

from mainapp.models import AutomationProject, Article, KeywordProject, Keyword
from mainapp.utils import (publish_article_to_wix, publish_article_to_wp, publish_article_to_wf,
                           create_bulk_article_generation_jobs, publish_article_to_shopify)

from mainapp.article_title_gen_v2.titles_gen_v2 import ArticleTitleGeneratorV2

logger = logging.getLogger(__file__)

class Command(BaseCommand):
    delay: int = 600  # in seconds

    def handle(self, *args, **kwargs):
        while True:
            self.publish_automation_projects()
            time.sleep(self.delay)

    def publish_automation_projects(self):
        current_datetime = datetime.datetime.now(tz=ZoneInfo("UTC"))
        current_day = current_datetime.strftime("%A").lower()  # e.g., 'monday'
        current_time = current_datetime.time()

        # Fetch Automation Projects that need to be published
        projects_to_publish = AutomationProject.objects.filter(
            auto_publish_state__in=["on", "draft"],  # only consider projects that are 'on' or 'draft'
            auto_publish_days__icontains=current_day,  # only consider projects that are scheduled to be published on the current day
        )

        # Filter further based on auto_publish_time
        for project in projects_to_publish:
            if self.is_project_actionable_now(project, current_time):
                self.take_action_on_project(project)
            else:
                self.generate_articles(project)

    def generate_articles(self, project: AutomationProject):
        """
        Generate articles 3 hours before the publish time.
        """
        current_time = datetime.datetime.now(tz=ZoneInfo("UTC"))
        if (
            project.auto_publish_time == "any_time" or
            project.publish_only_generated_articles or
            (project.last_ran_on and project.last_ran_on.date() == current_time.date()) or 
            project.articles_generated
        ):
            return None

        else:
            if (
                project.auto_publish_time == "morning" and current_time.hour >= 3 or
                project.auto_publish_time == "afternoon" and current_time.hour >= 9 or
                project.auto_publish_time == "evening" and current_time.hour >= 15 or
                project.auto_publish_time == "night" and current_time.hour >= 22
            ):
                arts_to_pub_per_run = project.article_count
                project_name = project.project_name
                kwds_matching_req_criteria = self.get_keywords_matching_criteria(project)

                if not kwds_matching_req_criteria:
                    project.auto_publish_state = "completed"
                    project.save()
                    logger.info(f"[*] '{project_name}' automation project completed.")
                    return None

                arts_to_publish = self.get_articles_to_publish(kwds_matching_req_criteria, project)
                non_generated_arts_matching_req_criteria = self.get_non_generated_articles_to_publish(kwds_matching_req_criteria, project,
                                                                                                      arts_to_pub_per_run - len(arts_to_publish))

                if len(arts_to_publish) < arts_to_pub_per_run:
                    logger.info(f"[*] Not enough generated articles to publish for '{project.project_name}' keywords. Generating new articles...")

                    if len(non_generated_arts_matching_req_criteria) < arts_to_pub_per_run - len(arts_to_publish):
                        logger.info(f"[*] Not enough article titles to generate for '{project.project_name}'. Generating new titles...")

                        for keyword in kwds_matching_req_criteria:
                            if arts_to_pub_per_run - len(arts_to_publish) - len(non_generated_arts_matching_req_criteria) <= 0:
                                break

                            try:
                                art = ArticleTitleGeneratorV2(user=project.user)
                                title_list = art.generate_titles(
                                    keyword.keyword_md5_hash,
                                    location=project.associated_keyword_project.location_iso_code,
                                    title_count=10
                                )
                                if isinstance(title_list, str):
                                    raise Exception(title_list)
                                titles_generated = len(title_list)
                                project.user.titles_generated += titles_generated
                                project.user.total_titles_generated += titles_generated
                                project.user.save()

                            except Exception as err:
                                logger.exception(err)
                                logger.error(f"[*] Failed to generate article titles for '{keyword.keyword}'")

                        # Refetch the non-generated articles to publish
                        non_generated_arts_matching_req_criteria = self.get_non_generated_articles_to_publish(kwds_matching_req_criteria, project,
                                                                                                            arts_to_pub_per_run - len(arts_to_publish))

                    article_uids = [art.article_uid for art in non_generated_arts_matching_req_criteria[:arts_to_pub_per_run - len(arts_to_publish)]]
                    # Not passing the automation project instance to avoid publishing the article
                    bulk_art_gen_jobs_res = create_bulk_article_generation_jobs(article_uids,
                                                                                project.user,
                                                                                retry_count=20,
                                                                                delay=120,
                                                                                max_pod_per_node=70,
                                                                                cluster_load_threshold=55)

                    if bulk_art_gen_jobs_res.get("status") == "success":
                        logger.info(f"[*] {arts_to_pub_per_run - len(arts_to_publish)} new articles were sent for generation for '{project.project_name}'")
                        project.articles_generated = True
                        project.save()

                    else:
                        logger.debug(f"[*] Failed to generate articles for '{project.project_name}'")

                        if bulk_art_gen_jobs_res.get("reason") == "max_limit_reached":
                            self.pause_project(project, f"Plan's Max article generation limit reached for '{project_name}'. Pausing project...")

                        elif bulk_art_gen_jobs_res.get("reason") == "no_articles_found":
                            self.pause_project(project, f"[*] Error: articles not found for the provided article_uids")

            else:
                return None

    def is_project_actionable_now(self, project: AutomationProject, current_time):
        """
        Check if the current time falls within the auto_publish_time range
        """
        timing_check = False

        if project.auto_publish_time == "any_time":
            timing_check = True
        elif project.auto_publish_time == "morning" and 6 <= current_time.hour < 12:
            timing_check = True
        elif project.auto_publish_time == "afternoon" and 12 <= current_time.hour < 18:
            timing_check = True
        elif project.auto_publish_time == "evening" and 18 <= current_time.hour < 24:
            timing_check = True
        elif project.auto_publish_time == "night" and 0 <= current_time.hour < 6:
            timing_check = True

        if not timing_check:
            return False

        # INTEGRATION CHECK
        if not project.user.all_integrations:
            # Pause the project if the user does not have any integration
            self.pause_project(project, f"User '{project.user.email}' does not have any integration ('WordPress', 'Webflow', 'Wix'). Pausing project...")
            return False

        # FREQUENCY CHECK
        if project.frequency == "daily" and project.last_ran_on and project.last_ran_on.date() == datetime.date.today():
            # if the project has already been run today
            logger.info(f"[*] '{project.project_name}' already ran today. Skipping...")
            return False

        elif project.frequency == "weekly" and project.last_ran_on and project.last_ran_on.date() + datetime.timedelta(days=7) > datetime.date.today():
            # if it's been less than a week since the last run
            logger.info(f"[*] '{project.project_name}' already ran this week. Skipping...")
            return False

        elif project.frequency == "monthly" and project.last_ran_on and project.last_ran_on.date() + datetime.timedelta(days=30) > datetime.date.today():
            # check if it's been more than a month since the last run
            logger.info(f"[*] '{project.project_name}' already ran this month. Skipping...")
            return False

        return True

    def take_action_on_project(self, project: AutomationProject):
        project_name = project.project_name
        arts_to_pub_per_run = project.article_count
        kwds_matching_req_criteria = self.get_keywords_matching_criteria(project)

        if not kwds_matching_req_criteria:
            project.auto_publish_state = "completed"
            project.save()
            logger.info(f"[*] '{project_name}' automation project completed.")
            return None

        arts_to_publish = self.get_articles_to_publish(kwds_matching_req_criteria, project)

        if project.publish_only_generated_articles and not arts_to_publish:
            self.pause_project(project, f"publish_only_generated_articles is enabled but no already generated articles found matching the criteria.")
            return None

        non_generated_arts_matching_req_criteria = self.get_non_generated_articles_to_publish(kwds_matching_req_criteria, project, arts_to_pub_per_run - len(arts_to_publish))

        if not project.publish_only_generated_articles and len(arts_to_publish) < arts_to_pub_per_run:
            logger.info(f"[*] Not enough generated articles to publish for '{project.project_name}' keywords. Generating new articles...")

            if len(non_generated_arts_matching_req_criteria) < arts_to_pub_per_run - len(arts_to_publish):
                logger.info(f"[*] Not enough article titles to generate for '{project.project_name}'. Generating new titles...")

                for keyword in kwds_matching_req_criteria:
                    if arts_to_pub_per_run - len(arts_to_publish) - len(non_generated_arts_matching_req_criteria) <= 0:
                        break

                    try:
                        art = ArticleTitleGeneratorV2(user=project.user)
                        title_list = art.generate_titles(
                            keyword.keyword_md5_hash,
                            location=project.associated_keyword_project.location_iso_code,
                            title_count=10
                        )
                        if isinstance(title_list, str):
                            raise Exception(title_list)
                        titles_generated = len(title_list)
                        project.user.titles_generated += titles_generated
                        project.user.total_titles_generated += titles_generated
                        project.user.save()

                    except Exception as err:
                        logger.exception(err)
                        logger.error(f"[*] Failed to generate article titles for '{keyword.keyword}'")

                # Refetch the non-generated articles to publish
                non_generated_arts_matching_req_criteria = self.get_non_generated_articles_to_publish(kwds_matching_req_criteria, project,
                                                                                                      arts_to_pub_per_run - len(arts_to_publish))

            article_uids = [art.article_uid for art in non_generated_arts_matching_req_criteria[:arts_to_pub_per_run - len(arts_to_publish)]]
            bulk_art_gen_jobs_res = create_bulk_article_generation_jobs(article_uids,
                                                                        project.user,
                                                                        project,
                                                                        retry_count=20,
                                                                        delay=10,
                                                                        max_pod_per_node=70,
                                                                        cluster_load_threshold=55)

            if bulk_art_gen_jobs_res.get("status") == "success":
                logger.info(f"[*] {arts_to_pub_per_run - len(arts_to_publish)} new articles were sent for generation for '{project.project_name}'")
                project.published_articles_count += len(article_uids)
                project.save()

            else:
                logger.debug(f"[*] Failed to generate articles for '{project.project_name}'")

                if bulk_art_gen_jobs_res.get("reason") == "max_limit_reached":
                    self.pause_project(project, f"Plan's Max article generation limit reached for '{project_name}'. Pausing project...")

                elif bulk_art_gen_jobs_res.get("reason") == "no_articles_found":
                    self.pause_project(project, f"[*] Error: articles not found for the provided article_uids")

        # Publish articles
        status = "draft" if project.auto_publish_state == "draft" else "publish"
        for article in arts_to_publish:
            if project.published_articles_count >= project.article_count:
                break
            res = None
            if "wordpress" in project.selected_integration_name and project.user.wordpress_integrations.exists():
                res = publish_article_to_wp(article, project.user, status, project.selected_integration_unique_text_id)
            elif "webflow" in project.selected_integration_name and project.user.webflow_integrations.exists():
                res = publish_article_to_wf(article, project.user, project.selected_integration_unique_text_id, status)
            elif "wix" in project.selected_integration_name and project.user.wix_integrations.exists():
                res = publish_article_to_wix(article, status, project.selected_integration_unique_text_id)
            elif "shopify" in project.selected_integration_name and project.user.shopify_integrations.exists():
                res = publish_article_to_shopify(article, status, project.selected_integration_unique_text_id)
            else:
                self.pause_project(project, f"[*] No integration found for '{project.project_name}'")
                return None

            article.associated_automation_project = project
            article.save()

            if res and res["status"] == "success":
                project.published_articles_count += 1
                project.save()
            elif res and res.get("error_message"):
                logger.error(f"{res['error_message']}")
            else:
                logger.error(f"Could not auto publish article {article.article_uid} to {project.selected_integration_name}")

        # Update project's last_ran_on date and reset published_articles_count
        # published_articles_count is reset to 0 after the articles have been published.
        # This count is used to keep track of how many articles have been published in the current run.
        # Resetting it ensures that the count starts fresh for the next run, preventing any carry-over from previous runs.
        logger.info(f"[*] {project.published_articles_count} articles were posted out of {project.article_count}")
        project.last_ran_on = datetime.datetime.now(tz=ZoneInfo("UTC"))
        project.published_articles_count = 0
        project.articles_generated = False
        project.save()

    def get_keywords_matching_criteria(self, project: AutomationProject) -> List[Keyword]:
        """Get keywords matching the specified criteria for the project."""
        related_kwd_project: KeywordProject = project.associated_keyword_project
        published_arts_under_project: List[Article] = Article.objects.filter(is_posted=True, associated_automation_project=project)

        kwds_matching_req_criteria = Keyword.objects.filter(
            keywordproject=related_kwd_project,
            volume__gte=project.keywords_traffic_range_min,
            volume__lte=project.keywords_traffic_range_max
        ).exclude(keyword_md5_hash__in=[art.keyword.keyword_md5_hash for art in published_arts_under_project])

        return list(set(kwds_matching_req_criteria))

    def get_articles_to_publish(self, kwds_matching_req_criteria, project: AutomationProject) -> List[Article]:
        arts_matching_req_criteria = Article.objects.filter(keyword__in=kwds_matching_req_criteria, is_posted=False,
                                                            is_processing=False, associated_automation_project=None)
        published_arts_under_project = Article.objects.filter(is_posted=True, associated_automation_project=project)
        return [
            art for art in arts_matching_req_criteria
            if art.is_generated and art.article_uid not in [published_art.article_uid for published_art in published_arts_under_project]
        ]

    def get_non_generated_articles_to_publish(self, kwds_matching_req_criteria, project: AutomationProject, required_count: int) -> List[Article]:
        non_generated_arts_matching_req_criteria = []

        for kwd in kwds_matching_req_criteria:
            # randomly fetch one article from the list of non-generated articles
            arricle: Article = Article.objects.filter(keyword=kwd, is_posted=False,
                                                      is_processing=False, associated_automation_project=None).order_by('?').first()

            if arricle:
                non_generated_arts_matching_req_criteria.append(arricle)

        return non_generated_arts_matching_req_criteria[:required_count]

    def pause_project(self, project: AutomationProject, message: str):
        logger.info(f"[*] {message}")
        project.auto_publish_state = "paused"
        project.save()
