"""
Django management command to automatically scan websites for new pages.
This command should be run as a cronjob every 24 hours.

It processes websites that have auto_scan_website=True by:
1. Creating website scanning jobs that will fetch sitemap URLs
2. The scanning process handles URL deduplication and filtering automatically
3. Only new/unscanned pages will be processed
"""

import logging
from django.core.management.base import BaseCommand
from django.db.models import QuerySet

from mainapp.models import Website, WebsiteScanQueue
from mainapp.utils import sanitize_url

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Automatically scan websites for new pages (run as daily cronjob)'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run in dry-run mode without creating actual scanning jobs',
        )
        parser.add_argument(
            '--website-id',
            type=int,
            help='Process only a specific website by ID (for testing)',
        )

    def handle(self, **options):
        dry_run = options.get('dry_run', False)
        website_id = options.get('website_id')
        
        self.stdout.write('Starting auto-scan websites cronjob...')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('Running in DRY-RUN mode - no scanning jobs will be created'))

        try:
            websites_processed = self.process_auto_scan_websites(dry_run, website_id)
            self.stdout.write(
                self.style.SUCCESS(f'Successfully processed {websites_processed} websites')
            )
        except Exception as e:
            logger.critical(f"Error in auto-scan websites cronjob: {str(e)}")
            self.stdout.write(self.style.ERROR(f'Error: {str(e)}'))
            raise

    def process_auto_scan_websites(self, dry_run: bool = False, website_id: int = None) -> int:
        """
        Process websites that have auto_scan_website enabled
        
        Args:
            dry_run: If True, don't create actual scanning jobs
            website_id: If provided, process only this specific website
            
        Returns:
            Number of websites processed
        """
        # Get websites with auto-scan enabled
        websites_query = Website.objects.filter(auto_scan_website=True)
        
        if website_id:
            websites_query = websites_query.filter(id=website_id)
            
        # Exclude websites that are currently being processed
        websites_query = websites_query.exclude(
            is_crawling=True
        ).exclude(
            task_queued=True
        ).exclude(
            websitescanqueue__status__in=['queued', 'processing']
        )
        
        websites: QuerySet[Website] = websites_query.distinct()
        
        if not websites.exists():
            self.stdout.write('No websites found for auto-scanning')
            return 0
            
        websites_processed = 0
        
        for website in websites:
            try:
                # Check if website has sitemaps configured
                if not website.sitemap_urls:
                    self.stdout.write(f'No sitemaps configured for {website.domain}, skipping...')
                    continue

                self.stdout.write(f'Processing auto-scan for {website.domain}')

                if not dry_run:
                    self.create_scanning_job(website)
                    self.stdout.write(
                        self.style.SUCCESS(f'Created auto-scan job for {website.domain}')
                    )
                else:
                    self.stdout.write(
                        f'DRY-RUN: Would create auto-scan job for {website.domain}'
                    )

                websites_processed += 1

            except Exception as e:
                logger.error(f"Error processing website {website.domain}: {str(e)}")
                self.stdout.write(
                    self.style.ERROR(f'Error processing {website.domain}: {str(e)}')
                )
                continue
                
        return websites_processed

    def create_scanning_job(self, website: Website) -> None:
        """
        Create a website scanning job that will automatically fetch and filter URLs

        Args:
            website: Website instance
        """
        # Check if there's already a queued or processing task for this website
        existing_task = WebsiteScanQueue.objects.filter(
            website=website,
            status__in=['queued', 'processing']
        ).first()

        if existing_task:
            self.stdout.write(
                self.style.WARNING(f'Scanning task already exists for {website.domain}')
            )
            return

        sanitized_sitemap_urls = []
        if website.sitemap_urls:
            sanitized_sitemap_urls = [sanitize_url(url) for url in website.sitemap_urls if sanitize_url(url)]

        # Create the scanning queue entry
        # The scanning process will automatically:
        # 1. Fetch URLs from the stored sitemaps
        # 2. Filter out already scanned URLs
        # 3. Process only new/unscanned pages
        # For auto-scan, use 'all' to run all tools for newly discovered pages
        try:
            WebsiteScanQueue.objects.create(
                website=website,
                sitemap_urls=sanitized_sitemap_urls,
                website_urls=[],  # Empty - let the scanning process fetch URLs from sitemaps
                request_from_admin=False,  # This is an automated scan
                tool_to_run='all'  # Auto-scan runs all tools for comprehensive scanning
            )
        except Exception as e:
            logger.critical(f"Error creating auto-scan WebsiteScanQueue for {website.domain}: {str(e)}")
            return

        # Mark website as task queued
        website.task_queued = True
        website.save()

        logger.info(f"Created auto-scan job for {website.domain} - will fetch and process new URLs from sitemaps")
