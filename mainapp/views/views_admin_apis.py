import datetime
import html
import logging
import tldextract
import urllib.parse
from typing import List, Dict, Literal
import csv
import redis
import stripe
import pytz
from collections import OrderedDict

from django.contrib.auth.hashers import check_password
from django.db import transaction
from django.utils import timezone
from django.db.models import (Count, QuerySet, Sum, F, Value, Subquery, OuterRef,
                              IntegerField, Q, Prefetch, IntegerField, Q, Func)

from django.db.models.functions import Coalesce
from django.contrib.postgres.aggregates import ArrayAgg
from django.http import JsonResponse
from django.core.paginator import Paginator, EmptyPage, InvalidPage
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.request import Request
from rest_framework_simplejwt.tokens import RefreshToken

from AbunDRFBackend.settings import (ADMIN_SECRET, REDIS_HOST, REDIS_PORT, REDIS_MINI_AI_TOOL_DB, DEBUG,
                                     FLY_ARTICLE_GEN_APP_NAME, FLY_ARTICLE_GEN_DEPLOY_TOKEN)
from mainapp.decorators import admin_only
from mainapp.json_responses import JsonResponseBadRequest, JsonResponseKeyError, JsonResponseServerError, JsonResponseGone
from mainapp.models import (AdminStats, User, Website, KubernetesJob, IgnoredCompetitor, AllArticlesStats,
                            BlockDomain, Article, GPT4UsageStats, BlockWebsiteKeywords,
                            BlockKeywords, KeywordProject, Keyword, SerperResults, BackLink, AutoCoupon, Survey,
                            AutomationProject, WebPage, BlogFinder, BlogFinderProject, GlossaryContent, GlossaryTopic,
                            GuestPostFinderQuery, GuestPostFinderResult, RedditPostFinderQuery, RedditPostFinderResult, ChangeLog, InstructionAndContext, WordpressPublishedArticle)
from mainapp.serializers import (AdminStatsSerializer, AllAdminsSerializer, ArticleTitleTableDataSerializer,
                                 KeywordProjectsSerializer, KeywordProjectKeywordsSerializer,ArticleSerializer,
                                 SerperResultsSerializer, CSVUploadSerializer, BackLinkSerializer, AutoCouponSerializer,
                                 WebflowSitesSerializer, WordpressSitesSerializer, WixSitesSerializer, ShopifyShopsSerializer,
                                 BlogFinderSerializer, BlogFinderProjectSerializer,
                                 GuestPostFinderQuerySerializer, GuestPostFinderResultSerializer, RedditPostFinderQuerySerializer,
                                 RedditPostFinderResultSerializer, ChangeLogSerializer, GhostSitesSerializer, GHLSitesSerializer, WordpressPublishedArticleSerializer, GoogleSerializer)
from mainapp.tasks import celery_check_flyio_provisioning, celery_start_website_scanning
from mainapp.utils import *
from mainapp.stripe_utils import get_stripe_product_data, get_user_plan_name_by_product_id, fetch_all_stripe_products, get_user_subscription_history
from mainapp.chroma_db_manager import ChromaDBManager
from mainapp.email_messages import (unable_to_publish_article_body)
from mainapp.custom_featured_images import generate_custom_feature_image
from mainapp.google_integration_utils import get_verified_gsc_sites


logger = logging.getLogger('abun.admin.api')


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_authenticate(_: Request):
    """
    Endpoint to authenticate admin user. Used for page authentications (login redirections).
    """
    return JsonResponse(status=200, data={'message': "Authenticated"})


@api_view(['POST'])
def admin_signup(request: Request):
    """
    Creates new admin account or converts existing user account to admin account.
    """
    try:
        username: str = html.escape(request.data['username']).strip()
        email: str = html.escape(request.data['email']).strip().lower()
        password: str = request.data['password']
        admin_secret: str = request.data['secret']
    except KeyError as k:
        logger.error(f"admin login - Missing key {k}")
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY"})

    logger.debug(username)
    logger.debug(email)
    logger.debug(password)
    logger.debug(admin_secret)

    if admin_secret == ADMIN_SECRET:
        try:
            user = User.objects.get(email=email)
            user.admin = True
            user.save()
        except User.DoesNotExist:
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                admin=True,
                country="India",
                user_tz="Asia/Kolkata"
            )
            user.save()

        # Generate jwt tokens and send them back in response
        tokens = RefreshToken.for_user(user)

        return JsonResponse(status=200, data={
            'refresh_token': str(tokens),
            'access_token': str(tokens.access_token),
        })

    else:
        # Wrong Secret passcode
        return JsonResponse(status=403, data={'message': "Wrong Secret"})


@api_view(['POST'])
def admin_login(request: Request):
    """
    Peforms login for admin user.
    """
    try:
        email: str = html.escape(request.data['email']).strip().lower()
        password: str = request.data['password']
        admin_secret: str = request.data['secret']
    except KeyError as k:
        logger.error(f"admin login - Missing key {k}")
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY"})

    logger.debug(email)
    logger.debug(password)
    logger.debug(admin_secret)

    if admin_secret == ADMIN_SECRET:
        try:
            user: User = User.objects.filter(email=email, admin=True)[0]
        except IndexError:
            return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_ADMIN"})

        if check_password(password, user.password):
            # Generate jwt tokens and send them back in response
            tokens = RefreshToken.for_user(user)

            return JsonResponse(status=200, data={
                'refresh_token': str(tokens),
                'access_token': str(tokens.access_token),
            })

        else:
            # Wrong Password
            return JsonResponse(status=403, data={'message': "Wrong Password"})

    else:
        # Wrong Secret Passcode
        return JsonResponse(status=403, data={'message': "Wrong Secret"})


@api_view(['POST'])
@permission_classes([AllowAny])
def admin_logout(request: Request):
    """
    Blacklists provided refresh token.
    """
    refresh_token: str = request.data['refresh']
    token = RefreshToken(refresh_token)
    token.blacklist()
    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_dashboard(_request: Request):
    """
    Provides stats data for admin dashboard page.
    """
    recent_stats = AdminStatsSerializer(AdminStats.objects.all().first()).data
    today = datetime.date.today()
    last_7_days = today - datetime.timedelta(days=7)

    # Successful Articles Stats
    articles_generated_today = AllArticlesStats.objects.filter(generated_on__date=today, is_successful=True).count()
    articles_generated_in_last_7_days = AllArticlesStats.objects.filter(generated_on__date__gte=last_7_days, is_successful=True).count()
    all_time_articles_generated = AllArticlesStats.objects.filter(is_successful=True).count()

    # Failed Articles Stats
    failed_articles_today = AllArticlesStats.objects.filter(generated_on__date=today, is_successful=False).count()
    failed_articles_in_last_7_days = AllArticlesStats.objects.filter(generated_on__date__gte=last_7_days, is_successful=False).count()
    all_time_failed_articles = AllArticlesStats.objects.filter(is_successful=False).count()

    # OpenAI gpt3.5 Article gen Cost Stats
    all_time_openai_article_cost = float(KubernetesJob.objects.aggregate(
        total_cost=Sum('article_generation_cost_info')
    )['total_cost'] or 0)

    openai_article_cost_last_7_days = float(KubernetesJob.objects.filter(
        created_on__date__gte=last_7_days
    ).aggregate(total_cost=Sum('article_generation_cost_info'))['total_cost'] or 0)

    openai_article_cost_today = float(KubernetesJob.objects.filter(
        created_on__date=today
    ).aggregate(total_cost=Sum('article_generation_cost_info'))['total_cost'] or 0)

    # GPT-4 Usage Stats
    gpt4_usage_cost_today = float(GPT4UsageStats.objects.filter(
        created_on__date=today
    ).aggregate(total_cost=Sum('usage_cost'))['total_cost'] or 0)

    gpt4_usage_cost_last_7_days = float(GPT4UsageStats.objects.filter(
        created_on__date__gte=last_7_days
    ).aggregate(total_cost=Sum('usage_cost'))['total_cost'] or 0)

    gpt4_usage_cost_all_time = float(GPT4UsageStats.objects.aggregate(
        total_cost=Sum('usage_cost')
    )['total_cost'] or 0)

    article_feedback_stats = {
        'total_feedbacks': Article.objects.filter(feedback__in=['positive', 'negative']).count(),
        'positive_feedbacks': Article.objects.filter(feedback='positive').count(),
        'negative_feedbacks': Article.objects.filter(feedback='negative').count(),
    }

    # LTD Plan Stats
    ltd_tier_1_users = User.objects.filter(appsumo_licenses__tier=1, appsumo_licenses__license_status='active').count()
    ltd_tier_2_users = User.objects.filter(appsumo_licenses__tier=2, appsumo_licenses__license_status='active').count()
    ltd_tier_3_users = User.objects.filter(appsumo_licenses__tier=3, appsumo_licenses__license_status='active').count()
    total_ltd_users = ltd_tier_1_users + ltd_tier_2_users + ltd_tier_3_users

    ltd_plan_stats = {
        'tier_1_users': ltd_tier_1_users,
        'tier_2_users': ltd_tier_2_users,
        'tier_3_users': ltd_tier_3_users,
        'total_ltd_users': total_ltd_users,
    }

    # Get the GPT-4.1-mini token usage from Redis
    with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
        try:
            total_token_used = redis_connection.get('token_used')
            if total_token_used:
                total_token_used = int(total_token_used)
            else:
                total_token_used = 0
        except TypeError:
            total_token_used = 0

    recent_stats.update({
        'articles_generated_today': articles_generated_today,
        'articles_generated_in_last_7_days': articles_generated_in_last_7_days,
        'all_time_articles_generated': all_time_articles_generated,
        'failed_articles_today': failed_articles_today,
        'failed_articles_in_last_7_days': failed_articles_in_last_7_days,
        'all_time_failed_articles': all_time_failed_articles,
        'all_time_openai_article_cost': all_time_openai_article_cost,
        'openai_article_cost_today': openai_article_cost_today,
        'openai_article_cost_last_7_days': openai_article_cost_last_7_days,
        'gpt4_usage_cost_today': gpt4_usage_cost_today,
        'gpt4_usage_cost_last_7_days': gpt4_usage_cost_last_7_days,
        'gpt4_usage_cost_all_time': gpt4_usage_cost_all_time,
        'article_feedback_stats': article_feedback_stats,
        "ltd_plan_stats": ltd_plan_stats,
        "gpt4_1_mini_tokens": total_token_used,
    })

    return JsonResponse(status=200, data=recent_stats)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_all_admins(_: Request):
    """
    Provides all admin account data.
    """
    return JsonResponse(
        status=200,
        data=AllAdminsSerializer(User.objects.filter(admin=True).order_by("-date_joined"), many=True).data,
        safe=False,
    )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_all_users(request: Request):
    """
    Provides all user data for admin All Users page with pagination.

    :param request: Django Rest Framework's Request object
    """
    try:
        all_users = request.GET.get('allUsers', False)
    except ValueError:
        all_users = False

    if not all_users:
        # Get pagination and filter parameters
        try:
            page = int(request.GET.get('page', 1))
        except ValueError:
            page = 1

        try:
            per_page = int(request.GET.get('per_page', 10))
        except ValueError:
            per_page = 1
    else:
        per_page = User.objects.all().count()
        page = 1

    search = request.GET.get('search', '')
    sort = request.GET.get('sort', '')

    # Subquery for counting keywords
    keywords_count = Keyword.objects.filter(website=OuterRef('current_active_website')) \
        .values('website') \
        .annotate(count=Count('pk')) \
        .values('count')

    # Subquery for counting keyword projects
    keyword_projects_count = KeywordProject.objects.filter(website=OuterRef('current_active_website')) \
        .values('website') \
        .annotate(count=Count('pk')) \
        .values('count')

    glossaryprojects_count = GlossaryTopic.objects.filter(
    website=OuterRef('pk')  # Match the relationship
    ).values('website').annotate(count=Count('pk')).values('count')

    glossarywords_count = GlossaryTopic.objects.filter(
    website=OuterRef('pk')  # Match the relationship
    ).annotate(
        words_length=Func(
            F('glossary_words'),
            Value(1),
            function='array_length',
            output_field=IntegerField()
        )
    ).values('website').annotate(
        total_words=Sum('words_length')
    ).values('total_words')

    # Base queryset with annotations
    base_queryset = User.objects.select_related('current_active_website').annotate(
        website_domain=Coalesce(F('current_active_website__domain'), Value("–––")),
        keywords_count=Subquery(keywords_count, output_field=IntegerField()),
        keyword_projects_count=Subquery(keyword_projects_count, output_field=IntegerField()),
        glossary_projects_count=Subquery(glossaryprojects_count, output_field=IntegerField()),
        glossary_words_count=Subquery(glossarywords_count, output_field=IntegerField()),
    )

    # Apply search if provided
    if search:
        # fetch all stripe products
        all_products = fetch_all_stripe_products()
        matching_product_id = next(
            (product['id'] for product in all_products
             if search.lower() in product['name'].lower()),
            None
        )

        search_query = Q(username__icontains=search) | \
            Q(email__icontains=search) | \
            Q(current_active_website__domain__icontains=search)

        if matching_product_id:
            search_query |= Q(stripe_product_id=matching_product_id)

        base_queryset = base_queryset.filter(search_query)

    # Apply sorting
    if sort:
        sort_fields = []
        for sort_item in sort.split(','):
            field, direction = sort_item.split(':')
            field_mapping = {
                'username': 'username',
                'email_id': 'email',
                'website': 'website_domain',
                'titles_generated': 'titles_generated',
                'articles_generated': 'articles_generated',
                'last_login': 'last_login',
                'date_joined': 'date_joined',
                'keywords': 'keywords_count',
                'keyword_projects': 'keyword_projects_count'
            }
            db_field = field_mapping.get(field, field)
            if direction == 'desc':
                db_field = f'-{db_field}'
            sort_fields.append(db_field)

        if sort_fields:
            base_queryset = base_queryset.order_by(*sort_fields)
    else:
        # Default sorting by date joined
        base_queryset = base_queryset.order_by('-date_joined')

    # Get total count and paginate in one query
    paginator = Paginator(base_queryset, per_page)

    try:
        paginated_users = paginator.page(page)
    except (EmptyPage, InvalidPage):
        paginated_users = paginator.page(paginator.num_pages)

    # Select specific fields in a single query
    users = paginated_users.object_list.values(
        "id", "username", "email", "website_domain",
        "titles_generated", "articles_generated",
        "last_login", "date_joined", "stripe_product_id",
        "keywords_count", "keyword_projects_count",
        "glossary_words_count", "glossary_projects_count"
    ).annotate(
        ltd_tiers=ArrayAgg(
            'appsumo_licenses__tier',
            filter=Q(appsumo_licenses__license_status='active'),
            ordering='appsumo_licenses__tier'
        )
    )

    # Format the response data
    response_data = {
        'users': [
            {
                **user,
                "website": user['website_domain'],
                "keywords": user['keywords_count'] or 0,
                "keyword_projects": user['keyword_projects_count'] or 0,
                "plan_name": format_ltd_plan_name(user['ltd_tiers']) or \
                             get_user_plan_name_by_product_id(user['stripe_product_id']),
                "glossary_words": user['glossary_words_count'] or 0,
                "glossary_projects": user['glossary_projects_count'] or 0,
            }
            for user in users
        ],
        'total': paginator.count
    }

    return JsonResponse(status=200, data=response_data, safe=False)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_user_data(request: Request):
    """
    Returns data for particular user using user id.
    """
    try:
        user_id: int = int(request.query_params['userId'])
    except KeyError as k:
        logger.error(f"admin_get_user_data() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        user: User = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_get_user_data() - No User with id {user_id}")
        return JsonResponseBadRequest()

    try:
        current_plan_data = get_stripe_product_data(user)
        current_plan_name = current_plan_data['display_name']
        website_connection_limit = current_plan_data['metadata']['max_websites']
    except Exception as err:
        logger.critical(f"admin_get_user_data() - Error fetching current plan data: {err}")
        current_plan_name = 'Not Found'
        website_connection_limit = 1

    kubernetes_jobs: QuerySet[KubernetesJob] = user.kubernetesjob_set.all().order_by('-created_on')
    articles = {}
    article_models = {
        "article-": Article,
    }

    for prefix, model in article_models.items():
        metadata_list = [task.metadata for task in kubernetes_jobs if task.metadata.startswith(prefix)]
        if metadata_list:
            articles.update({article.article_uid: article for article in model.objects.filter(article_uid__in=metadata_list)})

    k8_tasks = []
    for task in kubernetes_jobs:
        metadata = task.metadata
        article = articles.get(metadata)

        if article:
            task_info = f"{unescape_amp_char(article.title)} | {task.status} | {article.created_on.astimezone(pytz.timezone('Asia/Kolkata')).strftime('%d{suffix} %b, %y, %I:%M %p').replace('1{suffix}', '1st').replace('2{suffix}', '2nd').replace('3{suffix}', '3rd').replace('{suffix}', 'th')}"
            job_id = task.job_id
        else:
            task_info = f"Not Found | {task.status}"
            job_id = task.job_id

        k8_tasks.append({"job_id":job_id, "task_info":task_info})

    # Get the most recent article timestamp for each project
    latest_article_timestamp_subquery = Article.objects.filter(
        website__user=user,
        keyword__in=KeywordProject.keywords.through.objects.filter(
            keywordproject_id=OuterRef('pk')
        ).values('keyword_id')
    ).order_by('-created_on').values('created_on')[:1]

    # Annotate the KeywordProject queryset
    keyword_projects_with_annotations = KeywordProject.objects.filter(website__user=user).annotate(
        most_recent_art_title_timestamp=Subquery(latest_article_timestamp_subquery),
        total_keywords=Count('keywords')
    )

    response_data = [
        {
            'projectName': project.project_name,
            'totalKeywords': project.total_keywords,
            'totalTrafficVolume': project.total_traffic_volume,
            'dateCreated': project.created_on.strftime('%b %d, %Y'),
            'projectId': str(project.project_id),  # Convert project_id to string
            'locationIsoCode': project.location_iso_code,
            'mostRecentArtTitleTimestamp': project.most_recent_art_title_timestamp.timestamp() if project.most_recent_art_title_timestamp else None,
        }
        for project in keyword_projects_with_annotations
    ]

    try:
        survey = Survey.objects.get(user=user)
        survey_data = survey.survey_data
    except Survey.DoesNotExist:
        survey_data = []

    user_articles = Article.objects.filter(website__user=user).exclude(article_uid__startswith="wp-").values(
        "article_uid",
        "title",
        "keyword__keyword",
        "keyword__keyword_md5_hash",
        "keyword__volume",
        "internal_link_count",
        "external_link_count",
        "image_count",
        "word_count",
        "is_processing",
        "is_generated",
        "is_posted",
        "is_failed",
        "is_archived",
        "article_link",
        "is_user_added",
        "posted_to",
        "created_on",
        "generated_on",
        "feedback",
    ).order_by('-created_on')

    published_articles = AutomationProject.objects.filter(website__user=user).prefetch_related(
        Prefetch(
            'article_set',
            queryset=Article.objects.filter(is_posted=True),
            to_attr='published_article_set'
        )
    ).order_by('-created_on')
    total_count = published_articles.count()

    automation_serialized_data = get_automation_projects_with_articles(published_articles)
    appsumo_licenses = user.active_ltd_plans

    blog_url_data = BlogFinder.objects.filter(
                website__user=user
            ).exclude(
                blog_finder_project__isnull=False
            ).order_by('-created_at')

    serialized_data = BlogFinderSerializer(blog_url_data, many=True).data

    blog_project_data = BlogFinderProject.objects.filter(
        website__user=user
    ).order_by('-created_at')

    project_serialized_data = BlogFinderProjectSerializer(blog_project_data, many=True).data

    # Fetch glossary projects
    glossary_project_list = []

    try:
        glossary_topics = GlossaryTopic.objects.filter(
                website__user=user,
                word__isnull=False
            ).order_by('-created_at')

        total_glossary_words_count = sum(len(topic.glossary_words) for topic in glossary_topics)

        for glossary_topic in glossary_topics:
            count = len(glossary_topic.glossary_words) if glossary_topic else 0
            if count > 0:
                glossary_project_list.append({
                    'projectId': glossary_topic.project_id,
                    'projectName': f"glossary-topic-{glossary_topic.word[:15]}",
                    'glossaryWord': glossary_topic.word,
                    'glossaryWords': glossary_topic.glossary_words,
                    'count': count
                })

    except Exception as e:
        logger.error(f"Error fetching glossary projects: {str(e)}")

    # Fetch guest post finder queries
    guest_post_finder_queries = GuestPostFinderQuery.objects.filter(website__user=user).values(
        "id", "query", "limit", "is_processing", "created_at"
    ).order_by("-created_at")
    queries = GuestPostFinderQuerySerializer(guest_post_finder_queries, many=True).data

    # Total GPF count
    total_guest_queries_count = (
        GuestPostFinderResult.objects.filter(guest_post_finder__website__user=user).count()
    )

    # Fetch reddit post finder queries
    reddit_post_finder_queries = RedditPostFinderQuery.objects.filter(website__user=user).values(
        "id", "query", "limit", "is_processing", "created_at"
    ).order_by("-created_at")
    reddit_queries = RedditPostFinderQuerySerializer(reddit_post_finder_queries, many=True).data

    # Total RPF count
    total_reddit_queries_count = (
        RedditPostFinderResult.objects.filter(reddit_post_finder__website__user=user).count()
    )

    articles_data = []
    for batch in batched(user_articles, 10):
        for article in batch:
            articles_data.append({
                "articleUID": article["article_uid"],
                "articleTitle": article["title"],
                "keyword": article["keyword__keyword"] if article["keyword__keyword"] else "",
                "keywordHash": article["keyword__keyword_md5_hash"] if article["keyword__keyword_md5_hash"] else "",
                "keywordTraffic": article["keyword__volume"] if article["keyword__volume"] else 0,
                "internalLinks": article["internal_link_count"] if article['internal_link_count'] else 0,
                "externalLinks": article["external_link_count"] if article['external_link_count'] else 0,
                "images": article["image_count"] if article['image_count'] else 0,
                "wordCount": article["word_count"] if article['word_count'] else 0,
                "isProcessing": article["is_processing"],
                "isGenerated": article["is_generated"],
                "isPosted": article["is_posted"],
                "isFailed": article["is_failed"],
                "isArchived": article["is_archived"],
                "postLink": article["article_link"] if article["article_link"] else "",
                "isUserAdded": article["is_user_added"],
                "postedTo": article["posted_to"] if article["posted_to"] else "",
                "createdOn": article["created_on"].astimezone(pytz.timezone('Asia/Kolkata')).strftime('%d{suffix} %b, %y, %I:%M %p').replace('1{suffix}', '1st').replace('2{suffix}', '2nd').replace('3{suffix}', '3rd').replace('{suffix}', 'th') if article["created_on"] else "",
                "generatedOn": article["generated_on"].astimezone(pytz.timezone('Asia/Kolkata')).strftime('%d{suffix} %b, %y, %I:%M %p').replace('1{suffix}', '1st').replace('2{suffix}', '2nd').replace('3{suffix}', '3rd').replace('{suffix}', 'th') if article["generated_on"] else "",
                "feedback": article["feedback"] if article['feedback'] else "",
            })

    # optimize published article
    optimize_published_article = WordpressPublishedArticle.objects.select_related("article").filter(website__user=user)
    optimize_published_article_serializer = WordpressPublishedArticleSerializer(optimize_published_article, many=True)

    return JsonResponse(
        status=200,
        data={
            'username': user.username,
            'email': user.email,
            'email_verified': user.verified,
            'country': user.country,
            'date_joined': user.date_joined,
            'last_login': user.last_login,
            'user_tz': user.user_tz,
            'titles_generated': user.titles_generated,
            'total_titles_generated': user.total_titles_generated,
            'blog_emails_found': user.blog_emails_found,
            'total_blog_emails_found': user.total_blog_emails_found,
            'articles_generated': user.articles_generated,
            'total_articles_generated': user.total_articles_generated,
            'total_keywords_generated': user.total_keywords_generated,
            'ai_calculators_generated': user.ai_calculators_generated,
            'total_ai_calculators_generated': user.total_ai_calculators_generated,
            'total_glossary_words_generated':total_glossary_words_count,
            'this_month_keywords_count': user.keywords_generated,
            'this_month_glossary_count':user.glossary_topic_generated,
            'website_count': user.website_set.count(),
            'website_connection_limit': website_connection_limit,
            'send_notification_emails': user.send_notification_emails,
            'current_active_website': user.current_active_website.domain if user.current_active_website else None,
            'current_plan': current_plan_name,
            'k8_tasks': k8_tasks,
            'articles': articles_data,
            "keyword_projects": KeywordProjectsSerializer(response_data, many=True).data,
            "glossary_projects": glossary_project_list,

            # get all connected website
            'all_connected_websites': [{
                'id': website.id,
                'name': website.name,
                'domain': website.domain,
                'logo': website.logo_url,
                'website_scan_status': website.crawling_status,
                'has_more_pages': website.has_more_pages,
                'pages_scanned': website.total_pages_scanned,
                # 'image_source': website.image_source,
            }  for website in user.website_set.all().order_by('-id') ],

            'website_logs': [],

            # Get survey details
            'survey_details': [
                {
                    'question': list(data.keys())[0],
                    'answer': list(data.values())[0],
                } for data in survey_data
            ],


            # Get Automatin Porjects
            'automation_projects': automation_serialized_data,
            'total_automation_projects': total_count,

            # Get the AppSumo LTD activation Dates
            'ltd_plans': [
                {
                    'plan_name': f"LTD Tier {appsumo_license.tier}",
                    'activated_on': appsumo_license.activated_on and appsumo_license.activated_on.astimezone(
                        pytz.timezone('Asia/Kolkata')
                    ).strftime('%d-%m-%Y %I:%M %p IST') or None,
                    'license_key': appsumo_license.license_key,
                } for appsumo_license in appsumo_licenses.all()
            ],

            # Get the all website scanning details
            'all_website_scanning_data': [
                {
                    'domain': website.domain,
                    'webpages': [
                        {
                            'url': webpage.url,
                            'title': webpage.title,
                            'summary': webpage.summary,
                            'lastScanned': webpage.last_scraped_on,
                        } for webpage in website.webpage_set.all()
                    ],
                    'is_website_crawling': website.is_crawling
                } for website in user.website_set.all()
            ],

            # Blog finder
            'blog_finder': serialized_data,
            'blog_finder_project': project_serialized_data,

            # Guest Post finder
            'guest_post_finder_queries': queries,
            'total_guest_queries_count': total_guest_queries_count,
            'this_month_guest_queries_count': user.guest_post_finder_queries_generated,

            # Reddit Post finder
            'reddit_post_finder_queries': reddit_queries,
            'total_reddit_queries_count': total_reddit_queries_count,
            'this_month_reddit_queries_count': user.reddit_post_finder_queries_generated,
            'subscription_history': get_user_subscription_history(user),
            'optimize_published_article': optimize_published_article_serializer.data,

        }
    )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_article_content(request: Request):
    """
    Returns article content.
    """
    def fetch_article(article_uid: str) -> Article | None:
        """
        Used to fetch the article from all the article models
        :param article_uid: Article UID
        """
        all_article_models: List[Article] = [Article]
        article: Article | None = None

        for model in all_article_models:
            try:
                article = model.objects.get(article_uid=article_uid)
                break
            except model.DoesNotExist:
                pass

        return article

    try:
        # We are using this api in 2 admin pages (task-logs & view-article)
        article_uid_or_k8_job_id: str = urllib.parse.unquote(request.query_params['articleUID'])
    except KeyError as k:
        logger.error(f"admin_get_user_data() - Missing key {k}")
        return JsonResponseBadRequest()

    article = fetch_article(article_uid_or_k8_job_id)

    if article is None:
        try:
            job_id = article_uid_or_k8_job_id.split(" ")[0]
            k8_job = KubernetesJob.objects.get(job_id=job_id)
            article = fetch_article(k8_job.metadata)

        except (IndexError, KubernetesJob.DoesNotExist):
            article = None

    if not article:
        logger.error(f"No article found with {article_uid_or_k8_job_id} UID or K8 Job ID")
        return JsonResponseBadRequest()
    
    serper_data = []
    top_ranking_articles = []
    serper_result = None
    
    if not article.article_uid.startswith('wp-'):
        try:
            serper_result: SerperResults = SerperResults.objects.get(keyword_hash=article.keyword.keyword_md5_hash)
        except SerperResults.DoesNotExist:
            logger.error(f"SerperResults not found for '{article.keyword.keyword_md5_hash}' keyword hash")
            serper_result = None

    if serper_result:
        serper_data = [result for result in serper_result.result]
        serper_urls = [result['url'] for result in serper_result.result]
        other_top_ranking_urls = article.other_top_ranking_urls

        for url in other_top_ranking_urls:
            try:
                index = serper_urls.index(url)
            except ValueError:
                continue

            data = serper_data.pop(index)
            serper_urls.pop(index)
            top_ranking_articles.append(data)

        top_ranking_articles.extend(serper_data)

    return JsonResponse(
        status=200,
        data={
            'article_title': unescape_amp_char(article.title),
            'processing': article.is_processing,
            'article_content': article.content,
            'summary_inputs': article.summary_inputs,
            'merged_summary': article.merged_summary,
            'crewai_output': article.crewai_output,
            'article_outlines': article.article_outlines,
            'article_feature_image': article.selected_featured_image.image_url if article.selected_featured_image else None,
            'serper_data': {"result": top_ranking_articles},
            'article_context': article.context,
            'article_posted': article.is_posted,
            "integrationWithUniqueID": article.user.all_integrations_with_unique_id,
            "selectedIntegration": article.user.all_integrations_with_unique_id[0] if len(article.user.all_integrations_with_unique_id) > 0 else None,
        }
    )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_keyword_project_data(request: Request):
    """
    Returns page data for a particular keyword project.

    :param request: Django Rest Framework's Request object
    """

    project_id = request.query_params['project_id']

    if not project_id:
        return JsonResponse({'error': 'Project ID is required'}, status=400)

    try:
        keyword_project = KeywordProject.objects.prefetch_related('keywords').get(project_id=project_id)
    except KeywordProject.DoesNotExist:
        return JsonResponse({'message': 'KeywordProject not found'}, status=404)

    keywords: List[Keyword] = keyword_project.keywords.all()
    response_data = []

    for keyword in keywords:
        is_titles_generated: bool = Article.objects.filter(keyword=keyword).exists()

        # getting the most recently generated article title's timestamp in Unix epoch time for the keyword
        if is_titles_generated:
            most_recent_art_title: Article = Article.objects.filter(keyword=keyword).order_by('-created_on').first()
            most_recent_art_title_timestamp = most_recent_art_title.created_on.timestamp()
        else:
            most_recent_art_title_timestamp = None

        response_data.append({
            'keyword': unescape_amp_char(keyword.keyword),
            'keywordHash': keyword.keyword_md5_hash,
            'keywordTraffic': keyword.volume,
            'difficultyScore': keyword.paid_difficulty,
            'titlesGenerated': is_titles_generated,
            'mostRecentArtTitleTimestamp': most_recent_art_title_timestamp,
            'kwVolume': getattr(keyword, 'kw_volume', False),
        })

    serializer = KeywordProjectKeywordsSerializer(response_data, many=True)

    return JsonResponse(serializer.data, status=200, safe=False)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_glossary_data(request: Request):
    """
    Fetch glossary data:
    - Combine GlossaryContent and GlossaryTopic terms.
    - Include all terms, with detailed fields for terms available in GlossaryContent.
    """
    user = request.user
    project_id = request.query_params.get('project_id')

    if not project_id:
        return JsonResponse(
            status=400,
            data={"error": "'project_id' is a required query parameter."}
        )

    try:
        # Fetch GlossaryContent
        glossary_contents = GlossaryContent.objects.filter(project_id=project_id)

        # Create a dictionary of terms with their details from GlossaryContent
        glossary_content_terms = {
            content.term: {
                "keyword_hash": content.keyword_hash,
                "is_generated": content.is_generated,
                "is_processing": content.is_processing,
                "is_archived": content.is_archived,
                "posted_to": content.posted_to,
                "feedback": content.feedback,
                "glossary_link": content.glossary_link,
            }
            for content in glossary_contents
        }

        # Fetch GlossaryTopic
        glossary_topic = GlossaryTopic.objects.filter(project_id=project_id).first()

        # Combine terms from GlossaryContent and GlossaryTopic
        combined_terms = []
        if glossary_topic:
            for term in glossary_topic.glossary_words:
                # Add term details from GlossaryContent if available, else only the term
                combined_terms.append({
                    "term": term,
                    **glossary_content_terms.get(term, {
                        "keyword_hash": None,
                        "is_generated": None,
                        "is_processing": None,
                        "is_archived": None,
                        "posted_to": None,
                        "feedback": None,
                        "glossary_link": None,
                    })
                })

        if combined_terms:
            return JsonResponse(
                status=200,
                data={
                    "message": "Glossary data successfully retrieved.",
                    "glossary_terms": combined_terms,
                }
            )

        # No data found
        return JsonResponse(
            status=404,
            data={"message": "No glossary content or topics found for the given project_id."}
        )

    except Exception as e:
        logger.error(f"Error fetching glossary terms: {str(e)}")
        return JsonResponse(
            data={"error": f"An error occurred: {str(e)}"},
            status=500,
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_glossary_content(request: Request):
    """
    Get glossary term and content based on keyword_hash for admin users.
    """
    user: User = request.user
    keyword_hash = request.query_params.get('keyword_hash')

    if not keyword_hash:
        return JsonResponse(
            status=400,
            data={"error": "'keyword_hash' is a required query parameter."}
        )

    try:
        # Fetch glossary content based on the provided keyword_hash
        glossary_content = GlossaryContent.objects.filter(keyword_hash=keyword_hash).first()

        if not glossary_content:
            return JsonResponse(
                status=404,
                data={"error": "No glossary content found for the given keyword_hash."}
            )

        return JsonResponse(
            status=200,
            data={
                "term": glossary_content.term,
                "content": glossary_content.content
            }
        )

    except Exception as e:
        logger.error(f"Error fetching glossary term by keyword_hash: {str(e)}")
        return JsonResponse(
            data={"error": f"An error occurred: {str(e)}"},
            status=500,
        )

@api_view(["GET"])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_guest_post_finder_data(request):
    """
    View to fetch GuestPostFinderResult details along with Hypestat data based on 'id' query parameter.
    """
    query_id = request.query_params.get('id')
    if not query_id:
        return JsonResponse({'success': False, 'message': "'id' query parameter is required."}, status=400)

    results = GuestPostFinderResult.objects.filter(
        guest_post_finder__id=query_id
    ).select_related('hypestat', 'guest_post_finder')

    if not results.exists():
        return JsonResponse({'success': False, 'message': 'No results found for this user and query ID.'}, status=404)

    # Serialize data
    serializer = GuestPostFinderResultSerializer(results, many=True)
    return JsonResponse(status=200, data={'success': True, 'queries': serializer.data})

@api_view(["GET"])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_reddit_post_finder_data(request):
    """
    View to fetch RedditPostFinderResult details along with Hypestat data based on 'id' query parameter.
    """
    query_id = request.query_params.get('id')
    if not query_id:
        return JsonResponse({'success': False, 'message': "'id' query parameter is required."}, status=400)

    results = RedditPostFinderResult.objects.filter(
        reddit_post_finder__id=query_id
    ).select_related('reddit_post_finder')

    if not results.exists():
        return JsonResponse({'success': False, 'message': 'No results found for this user and query ID.'}, status=404)

    # Serialize data
    serializer = RedditPostFinderResultSerializer(results, many=True)
    return JsonResponse(status=200, data={'success': True, 'queries': serializer.data})

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_article_titles_for_keywords_api(request: Request):
    """
    Returns data for current website's article titles.

    :param request: Django Rest Framework's Request object
    """

    if request.method == 'GET':
        try:
            keyword_hash: str = html.escape(request.query_params['keyword_hash'].strip())
        except Exception as err:
            logger.critical(err)
            return JsonResponse({'error': 'keyword_hash is required'}, status=400)

        try:
            keyword: Keyword = Keyword.objects.get(keyword_md5_hash=keyword_hash)
            user = keyword.website.user
        except Keyword.DoesNotExist:
            return JsonResponseBadRequest({'message': "Keyword not found for: {keyword_hash}"})

        except Exception as err:
            logger.critical(err)
            return JsonResponse({'error': 'something went wrong'}, status=500)

        try:
            serper_result: SerperResults = SerperResults.objects.get(keyword_hash=keyword_hash)
            serper_data = SerperResultsSerializer(serper_result).data

        except SerperResults.DoesNotExist:
            logger.error(f"No serp result found for '{keyword_hash}' keyword hash")
            serper_data = {"result": []}

        except Exception as err:
            logger.critical(err)
            serper_data = {"result": []}

        # subquery to get KeywordProject
        keyword_project_subquery = KeywordProject.objects.filter(website=OuterRef('website'))
        articles: QuerySet[Article] = Article.objects.select_related('keyword').filter(keyword=keyword).all().annotate(
            keyword_project_id=Subquery(keyword_project_subquery.values('project_id')[:1])
        )
        title_data = ArticleTitleTableDataSerializer(articles, many=True).data

        return JsonResponse(
            data={
                "title_data": title_data,
                "serper_data": serper_data,
                "keyword": unescape_amp_char(keyword.keyword),
                "selectedIntegration": user.all_integrations[0] if len(user.all_integrations) > 0 else None,
                "selectedIntegrationUniqueID": user.all_integrations_with_unique_id[0] if len(user.all_integrations_with_unique_id) > 0 else None,
                "integrationWithUniqueID": user.all_integrations_with_unique_id,
            },
            status=200,
            safe=False
        )

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_save_user_data(request: Request):
    """
    Saves updated User data from admin user details page.
    """
    try:
        user_id: int = request.data['user_id']
        email: str = request.data['email']
        email_verified: bool = request.data['email_verified']
        email_notifications: bool = request.data['email_notifications']
        country: str = request.data['country']
        user_tz: str = request.data['user_tz']
        titles_generated: int = int(request.data['titles_generated']) if 'titles_generated' in request.data else 0
        total_titles_generated: int = int(request.data['total_titles_generated']) if 'total_titles_generated' in request.data else 0
        articles_generated: int = int(request.data['articles_generated']) if 'articles_generated' in request.data else 0
        total_articles_generated: int = int(request.data['total_articles_generated']) if 'total_articles_generated' in request.data else 0
        blog_emails_found: int = int(request.data['blog_emails_found']) if 'blog_emails_found' in request.data else 0
        total_blog_emails_found: int = int(request.data['total_blog_emails_found']) if 'blog_emails_found' in request.data else 0
        total_keywords_generated: int = int(request.data['total_keywords_generated']) if 'total_keywords_generated' in request.data else 0
        this_month_keywords_count: int = int(request.data['this_month_keywords_count']) if 'this_month_keywords_count' in request.data else 0
        ai_calculators_generated: int = int(request.data['ai_calculators_generated']) if 'ai_calculators_generated' in request.data else 0
        total_ai_calculators_generated: int = int(request.data['total_ai_calculators_generated']) if 'total_ai_calculators_generated' in request.data else 0
        this_month_glossary_count: int = int(request.data['this_month_glossary_count']) if 'this_month_glossary_count' in request.data else 0
        this_month_guest_queries_count: int = int(request.data['this_month_guest_queries_count']) if 'this_month_guest_queries_count' in request.data else 0
        this_month_reddit_queries_count: int = int(request.data['this_month_reddit_queries_count']) if 'this_month_reddit_queries_count' in request.data else 0

    except KeyError as k:
        logger.error(f"admin_save_user_data() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        user: User = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_save_user_data() - No such user")
        return JsonResponseBadRequest()

    # Save all details
    user.email = email
    user.verified = email_verified
    user.send_notification_emails = email_notifications
    user.country = country
    user.user_tz = user_tz
    user.titles_generated = titles_generated
    user.total_titles_generated = total_titles_generated
    user.articles_generated = articles_generated
    user.total_articles_generated = total_articles_generated
    user.blog_emails_found = blog_emails_found
    user.total_blog_emails_found = total_blog_emails_found
    user.total_keywords_generated = total_keywords_generated
    user.keywords_generated = this_month_keywords_count
    user.glossary_topic_generated = this_month_glossary_count
    user.guest_post_finder_queries_generated = this_month_guest_queries_count
    user.reddit_post_finder_queries_generated = this_month_reddit_queries_count
    user.ai_calculators_generated = ai_calculators_generated
    user.total_ai_calculators_generated = total_ai_calculators_generated

    user.save()

    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_save_website_data(request: Request):
    """
    Saves updated Website data from admin user details page.
    """
    try:
        user_id: int = request.data['user_id']
        domain: str = request.data['domain']
        image_source: str = request.data['image_source']
    except KeyError as k:
        logger.error(f"admin_save_website_data() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        user: User = User.objects.get(id=user_id)
        website: Website = user.website_set.get(domain=domain)
    except User.DoesNotExist:
        logger.error(f"admin_save_website_data() - User with id {user_id} does not exists.")
        return JsonResponseBadRequest()
    except Website.DoesNotExist:
        logger.error(f"admin_save_website_data() - Website with domain {domain} does not exists on user id {user_id}.")
        return JsonResponseBadRequest()

    website.image_source = image_source
    website.save()

    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_delete_website(request: Request):
    """
    Deletes given website from user's account.
    """
    try:
        user_id: int = request.data['user_id']
        domain: str = request.data['domain']
    except KeyError as k:
        logger.error(f"admin_delete_website() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        user: User = User.objects.get(id=user_id)
        website: Website = user.website_set.get(domain=domain)
    except User.DoesNotExist:
        logger.error(f"admin_delete_website() - User with id {user_id} does not exists.")
        return JsonResponseBadRequest()
    except Website.DoesNotExist:
        logger.error(f"admin_delete_website() - Website with domain {domain} does not exists on user id {user_id}.")
        return JsonResponseBadRequest()

    # Check if user has other connected websites. If so, move their current active website to one of them.
    # Otherwise, set it as None
    remaining_websites = user.website_set.exclude(domain=domain)
    if remaining_websites:
        user.current_active_website = remaining_websites[0]
        user.save()
    else:
        user.current_active_website = None
        user.save()

    # Fetch All the webpages
    webpages = WebPage.objects.filter(website=website)

    # Delete ChromaDB data
    chroma_db_ids = list(webpages.values_list('embedding_id', flat=True))
    chromaDBManager = ChromaDBManager()
    chromaDBManager.delete_pages(chroma_db_ids, website.id)

    # Delete WebPages Data
    webpages.delete()

    # Delete the website
    website.delete()

    # Log the event
    add_website_log(
        user=user,
        domain=domain,
        message=f"{domain} disconnected by Admin.",
        connection_type='disconnected'
    )

    return JsonResponse(status=200, data={'message': "OK"})


# @api_view(['GET'])
# @permission_classes([IsAuthenticated])
# @admin_only
# def admin_get_website_content_plan_data(request: Request):
#     """
#     Returns data for particular user using user id.
#     """
#     try:
#         user_id: int = request.query_params['user_id']
#         website_id: int = request.query_params['website_id']
#     except KeyError as k:
#         logger.error(f"admin_get_website_content_plan_data() - Missing key {k}")
#         return JsonResponseBadRequest()

#     try:
#         user: User = User.objects.get(id=user_id)
#     except User.DoesNotExist:
#         logger.error(f"admin_get_website_content_plan_data() - User with id {user_id} does not exists.")
#         return JsonResponseBadRequest()

#     try:
#         website: Website = user.website_set.get(id=website_id)
#     except Website.DoesNotExist:
#         logger.error(f"admin_get_website_content_plan_data() - Website with id {website_id} does not exists "
#                      f"with user {user.email}")
#         return JsonResponseBadRequest()

#     content_plan_data = {
#         'status': website.content_plan_generation_status,
#         'started_on': website.content_plan_started_on,
#         'website_name': website.name,
#         'website_domain': website.domain,
#         'website_logo_url': website.logo_url,
#         'website_title': website.title,
#         'website_description': website.description,
#         'website_icp': website.icp_text,
#         'website_industry': website.industry,
#     }

#     if website.content_plan_generation_status == 'done':
#         total_comp_keywords: int = website.competitors.aggregate(total_comp_kw=Count('keywords'))['total_comp_kw']
#         total_keywords: int = website.keywords.count() + total_comp_keywords
#         total_articles: int = (website.keywords.count() + total_comp_keywords) * 3

#         content_plan_data.update({
#             'website_competitors': list(website.competitors.all().values_list('domain', flat=True)),
#             'performance_chart_url': website.content_plan_performance_chart_url,
#             'user_keywords': list(website.user_added_keywords.all().values_list('keyword', flat=True)),
#             'website_stats': [
#                 {'name': "Organic Traffic", 'value': website.organic_traffic},
#                 {'name': "Organic Keywords", 'value': website.organic_keywords},
#                 {'name': "Domain Authority", 'value': website.domain_authority},
#                 {'name': "Total Backlinks", 'value': website.total_backlinks},
#                 {'name': "Follow Count", 'value': website.follow_count},
#                 {'name': "No Follow Count", 'value': website.no_follow_count},
#                 {'name': "Referring Domains", 'value': website.referring_domains_count},
#                 {'name': "Total Possible Keywords", 'value': total_keywords},
#                 {'name': "Total Possible Articles", 'value': total_articles},
#             ]
#         })

#     return JsonResponse(
#         status=200,
#         data=content_plan_data
#     )


# @api_view(['POST'])
# @permission_classes([IsAuthenticated])
# @admin_only
# def admin_change_content_plan_status(request: Request):
#     """
#     Changes content plan status to given value.
#     """
#     try:
#         user_id: int = request.data['user_id']
#         website_id: int = request.data['website_id']
#         new_status: str = request.data['new_status']
#     except KeyError as k:
#         logger.error(f"admin_change_content_plan_status() - Missing key {k}")
#         return JsonResponseBadRequest()

#     try:
#         user: User = User.objects.get(id=user_id)
#     except User.DoesNotExist:
#         logger.error(f"admin_change_content_plan_status() - User with id {user_id} does not exists.")
#         return JsonResponseBadRequest()

#     try:
#         website: Website = user.website_set.get(id=website_id)
#     except Website.DoesNotExist:
#         logger.error(f"admin_change_content_plan_status() - Website with id {website_id} does not exists "
#                      f"with user {user.email}")
#         return JsonResponseBadRequest()

#     website.content_plan_generation_status = new_status
#     if new_status != 'done':
#         website.content_plan_task_progress = 0
#     website.save()

#     return JsonResponse(status=200, data={'message': "OK"})


# @api_view(['POST'])
# @permission_classes([IsAuthenticated])
# @admin_only
# def admin_retry_content_plan(request: Request):
#     """
#     Retries content plan generation K8 job.
#     """
#     try:
#         user_id: int = request.data['user_id']
#         website_id: int = request.data['website_id']
#     except KeyError as k:
#         logger.error(f"admin_retry_content_plan() - Missing key {k}")
#         return JsonResponseBadRequest()

#     try:
#         user: User = User.objects.get(id=user_id)
#     except User.DoesNotExist:
#         logger.error(f"admin_retry_content_plan() - User with id {user_id} does not exists.")
#         return JsonResponseBadRequest()

#     try:
#         website: Website = user.website_set.get(id=website_id)
#     except Website.DoesNotExist:
#         logger.error(f"admin_retry_content_plan() - Website with id {website_id} does not exists "
#                      f"with user {user.email}")
#         return JsonResponseBadRequest()

#     if website.content_plan_generation_status == 'processing':
#         return JsonResponseBadRequest({'err_id': "CONTENT_PLAN_ALREADY_PROCESSING"})

#     if website.content_plan_generation_status == 'done':
#         return JsonResponseBadRequest({'err_id': "CONTENT_PLAN_ALREADY_DONE"})

#     # Generate the new job id and create redis key using that.
#     content_plan_job_id = generate_k8_job_id('contentplan', website.domain)
#     content_plan_redis_key: str = content_plan_job_id + "-task-data"

#     # Fetch the content plan data. If not available for any reason, send 410 response.
#     task_data: Dict = website.content_plan_task_data
#     if not task_data:
#         logger.error("admin_retry_content_plan() - Data required for retrying content plan is missing.")
#         return JsonResponseGone(additional_data={'err_id': "TASK_DATA_MISSING"})

#     # Add this to redis task data db
#     with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
#         redis_connection.set(content_plan_redis_key, json.dumps(task_data))
#         redis_connection.expire(content_plan_redis_key, REDIS_CONTENT_PLAN_EXPIRY)

#     # Create K8 job
#     content_plan_k8_job = KubernetesJob(
#         job_id=content_plan_job_id,
#         user=user,
#         website=website,
#         status='running'
#     )
#     content_plan_k8_job.save()
#     create_k8_job(
#         content_plan_job_id,
#         'content_plan',
#         content_plan_job_id,
#         user.id,
#         [content_plan_job_id, content_plan_redis_key]
#     )

#     # Save job details to website
#     website.content_plan_generation_status = 'processing'
#     website.content_plan_started_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
#     website.save()

#     return JsonResponse(status=200, data={'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_task_details(request: Request):
    """
    Returns KubernetesJob details with KubernetesJobLogs for given task id (job_id).
    """
    try:
        user_id: int = int(request.query_params['user_id'])
        job_id: str = request.query_params['job_id']
    except KeyError as k:
        logger.error(f"admin_get_task_details() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        user: User = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_get_task_details() - User with id {user_id} does not exists.")
        return JsonResponseBadRequest()

    try:
        job_id = job_id.split("|")[0].strip()
        k8_job: KubernetesJob = user.kubernetesjob_set.get(job_id=job_id)
    except KubernetesJob.DoesNotExist:
        logger.error(f"admin_get_task_details() - KubernetesJob with job id {job_id} does not exists "
                     f"on user {user.email}")
        return JsonResponseBadRequest()

    try:
        article_uid = k8_job.metadata
        article: Article = Article.objects.get(article_uid=article_uid)
    except Article.DoesNotExist:
        article = None

    data = {
        "user_email": user.email,
        "username": k8_job.user.username,
        "created_on": k8_job.created_on,
        "status": k8_job.status,
        "retry_attempts": k8_job.retry_attempts,
        "metadata": k8_job.metadata,
        "keyword": unescape_amp_char(article.keyword.keyword) if article else None,
        "logs": [
            {
                "id": log.id,
                "type": log.type,
                "message": log.message,
                "created_on": log.created_on,
            }
            for log in k8_job.kubernetesjoblogs_set.all().order_by("created_on")
        ],
    }

    if "article-" in k8_job.metadata:
        try:
            article: Article = Article.objects.get(article_uid=k8_job.metadata)
            data["article-data"] = {
                "title": unescape_amp_char(article.title),
                "is_processing": article.is_processing,
                "is_failed": article.is_failed,
                "is_generated": article.is_generated,
                "is_posted": article.is_posted,
                "is_archived": article.is_archived,
                "cost": k8_job.article_generation_cost_info
            }
        except Article.DoesNotExist:
            logger.error(f"admin_get_task_details() - Article with uid {k8_job.metadata} does not exists.")

    return JsonResponse(
        status=200,
        data=data
    )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_ignored_competitor_domains(_: Request):
    """
    Returns ignored competitors domains list
    """

    return JsonResponse(
        status=200,
        data=list(IgnoredCompetitor.objects.all().values_list('domain', flat=True)),
        safe=False,
    )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_add_ignored_competitor_domains(request: Request):
    """
    Add ignored competitor domains
    """
    try:
        domains: list = request.data['domains']
    except KeyError as k:
        logger.error(f"admin_add_ignored_competitor_domains() - Missing key {k}")
        return JsonResponseBadRequest()

    duplicate_domains: list = []
    bad_domains: list = []

    try:
        # Extract domain name using tldextract from domain
        for domain in domains:
            domain_name = tldextract.extract(domain).registered_domain
            if domain_name and not IgnoredCompetitor.objects.filter(domain=domain_name).exists():
                new_ignored_competitor = IgnoredCompetitor(domain=domain)
                new_ignored_competitor.save()
            elif domain_name:
                duplicate_domains.append(domain_name)
            else:
                bad_domains.append(domain)
    except Exception as e:
        logger.error(f"admin_add_ignored_competitor_domains() - Error {e}")
        return JsonResponseBadRequest()

    return JsonResponse(
        status=200,
        data={
            "all_domains": list(
                IgnoredCompetitor.objects.all().values_list("domain", flat=True)
            ),
            "bad_domains": bad_domains,
            "duplicate_domains": duplicate_domains,
        },
    )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_remove_ignored_competitor_domains(request: Request):
    """
    Delete ignored competitor domains
    """
    try:
        domains: list = request.data['domains']
    except KeyError as k:
        logger.error(f"admin_remove_ignored_competitor_domains() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        # remove domains from IgnoredCompetitor
        IgnoredCompetitor.objects.filter(domain__in=domains).delete()
    except Exception as e:
        logger.error(f"admin_remove_ignored_competitor_domains() - Error {e}")
        return JsonResponseBadRequest()

    return JsonResponse(
        status=200,
        data={
            "all_domains": list(
                IgnoredCompetitor.objects.all().values_list("domain", flat=True)
            ),
        },
    )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_mini_tools_data(_: Request):
    """
    Fetch mini AI tool stats from redis

    :param request: Django Rest Framework's Request object
    """

    tool_list = ["AI-Meta-Description-Generator", "AI-Rewording", "AI-Blog-Title-Generator", "AI-Content-Idea-Generator",
                 "AI-Social-Media-Bio-Generator", "AI-Social-Media-Caption-Generator", "AI-Social-Media-Hashtag-Generator",
                 "AI-Social-Media-Username-Generator", "AI-YouTube-Video-Title-Generator", "AI-Brand-Name-Generator",
                 "AI-Business-Name-Generator", "AI-LinkedIn-Post-Idea-Generator", "AI-Color-Psychology-Generator",
                 "AI-Job-Description-Generator", "AI-Story-Generator", "AI-Business-Description-Generator",
                 "AI-Business-Slogan-Generator", "AI-NDA-Generator", "AI-Keyword-Generator", "AI-Video-Script-Generator"]

    tool_data = {}
    # Add this to redis task data db
    with get_redis_connection(db=REDIS_MINI_AI_TOOL_DB) as redis_connection:
        for tool in tool_list:
            last_seven_days_usage = len(redis_connection.keys(f"{tool}*"))
            tool_usage_counter = f'{tool}-counter'
            total_usage = redis_connection.get(tool_usage_counter)

            if total_usage:
                total_usage = total_usage.decode('utf-8')
            else:
                total_usage = "0"
                last_seven_days_usage = 0

            tool_data[f'{tool}'] = {
                    'last_seven_days_usage': 0 if last_seven_days_usage -1 < 0 else last_seven_days_usage - 1,
                    'total_usage': total_usage
                }

    data = {'tools': tool_list, 'tool_usage':tool_data}

    return JsonResponse(status=200, data=data)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_blocked_domains(_: Request):
    """
    Returns blocked domains list
    """
    return JsonResponse(
        status=200,
        data=list(BlockDomain.objects.all().values_list('domain', flat=True)),
        safe=False,
    )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_blocked_website_keywords(_: Request):
    """
    Returns blocked website keywords list
    """
    return JsonResponse(
        status=200,
        data=list(BlockWebsiteKeywords.objects.all().values_list('keyword', flat=True)),
        safe=False,
    )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_blocked_keywords(_: Request):
    """
    Returns blocked connect website keywords list
    """
    return JsonResponse(
        status=200,
        data=list(BlockKeywords.objects.all().values_list('keyword', flat=True)),
        safe=False,
    )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_add_block_domains(request: Request):
    """
    API to add Domains from blocked domains list
    params: Django Request object
    return: list of blocked domains
    """
    try:
        domains: list = request.data['domains']
        temp_domain_list = [domain.strip().replace(' ', '') for domain in domains]
        domain_list = list(set(temp_domain_list)) # Removing Duplicate entries via set

    except KeyError as k:
        logger.error(f"admin_block_domains() - Missing key {k}")
        return JsonResponseBadRequest()
    try:
        if len(domain_list) == 0:
            logger.error(f"admin_block_domains() - domains list should not empty")
            return JsonResponse(
            status=400,
            data={
                'message': 'domains list should not empty'
            },
        )

        blocked_domains_objects = []
        domain_names = [tldextract.extract(domain).registered_domain for domain in domain_list]
        existing_domains = BlockDomain.objects.filter(domain__in=domain_names).values_list('domain', flat=True)

        for domain_name in domain_names:
            if domain_name and (domain_name not in existing_domains):
                blocked_domains_objects.append(BlockDomain(domain=domain_name))

        if len(blocked_domains_objects) > 0:
            BlockDomain.objects.bulk_create(blocked_domains_objects)

        return JsonResponse(
            status=200,
            data={
                "all_domains": list(
                    BlockDomain.objects.all().values_list("domain", flat=True)
                ),
            },
        )
    except Exception as e:
        logger.error(f"admin_block_domains() - Error {e}")
        return JsonResponse(
            status=500,
            data={
                'message': 'Internal Server Error'
            },
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_add_block_website_keywords(request: Request):
    """
    API to add Keywords in blocked website or blocked keyword list
    params: Django Request object
    return: list of blocked keywords
    """
    try:
        keywords: List[str] = request.data['keywords']
        connect_website_keywords: bool = request.data.get('connect_website_keywords', False)
        temp_keywords_list = map(lambda keyword: keyword.strip().replace(' ', ''), keywords)
        keywords_list = list(set(temp_keywords_list)) # Removing Duplicate entries via set

    except KeyError as k:
        logger.error(f"admin_add_block_website_keywords() - Missing key {k}")
        return JsonResponseKeyError()

    try:
        if not keywords_list:
            logger.error(f"admin_add_block_website_keywords() - keywords list should not empty")
            return JsonResponse(
            status=400,
            data={
                'message': 'keywords list should not empty'
            },
        )

        if not connect_website_keywords:
            model = BlockWebsiteKeywords
        else:
            model = BlockKeywords

        blocked_keywords_objects = []
        existing_keywords = model.objects.filter(
            keyword__in=keywords_list
        ).values_list('keyword', flat=True)

        for keyword in keywords_list:
            if keyword and keyword not in existing_keywords:
                blocked_keywords_objects.append(model(keyword=keyword))

        if blocked_keywords_objects:
            model.objects.bulk_create(blocked_keywords_objects)

        return JsonResponse(
            status=200,
            data={
                "all_keywords": list(
                    model.objects.all().values_list("keyword", flat=True)
                ),
            },
        )

    except Exception as e:
        logger.critical(f"admin_add_block_website_keywords() - Error {e}")
        return JsonResponse(
            status=500,
            data={
                'message': 'Internal Server Error'
            },
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_remove_block_domains(request: Request):
    """
    API to remove Domains from blocked domains list
    params: Django Request object
    return: list of blocked domains
    """
    try:
        domains: list = request.data['domains']
    except KeyError as k:
        logger.error(f"admin_remove_block_domains() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        # remove domains from BlockDomain
        BlockDomain.objects.filter(domain__in=domains).delete()
    except Exception as e:
        logger.error(f"admin_remove_block_domains() - Error {e}")
        return JsonResponseBadRequest()

    return JsonResponse(
        status=200,
        data={
            "all_domains": list(
                BlockDomain.objects.all().values_list("domain", flat=True)
            ),
        },
    )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_remove_block_website_keywords(request: Request):
    """
    API to remove keywords from blocked website or blocked keywords list
    params: Django Request object
    return: list of blocked keywords
    """
    try:
        keywords: list = request.data['keywords']
        connect_website_keywords: bool = request.data.get('connect_website_keywords', False)
    except KeyError as k:
        logger.error(f"admin_remove_block_website_keywords() - Missing key {k}")
        return JsonResponseBadRequest()

    if not connect_website_keywords:
        model = BlockWebsiteKeywords
    else:
        model = BlockKeywords

    try:
        # remove domains/keywords from selected model
        model.objects.filter(keyword__in=keywords).delete()
    except Exception as e:
        logger.error(f"admin_remove_block_website_keywords() - Error {e}")
        return JsonResponseBadRequest()

    return JsonResponse(
        status=200,
        data={
            "all_keywords": list(
                model.objects.all().values_list("keyword", flat=True)
            ),
        },
    )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_update_article(request: Request):
    """
    Returns data for particular article using article id.
    """
    try:
        article_uid: str = request.data['article_uid']
        action: str = request.data['action']
    except KeyError as k:
        logger.error(f"admin_get_article_data() - Missing key {k}")
        return JsonResponseBadRequest()

    article: Article = Article.objects.get(article_uid=article_uid)

    if action == "mark_failed":
        article.is_generated = False
        article.is_processing = False
        article.is_failed = True
        article.save()

    elif action == "delete":
        article.delete()

    elif action == "regenerate":
        if article.is_processing:
            article.is_generated = False
            article.is_processing = False
            article.is_failed = True
            article.save()

        else:
            article.is_generated = False
            article.is_processing = False
            article.is_failed = False
            article.save()

        article_k8_task_response = create_article_generation_v2_task(article.user, article_uid,
                                                                     regenerate=True)

        if article_k8_task_response['status'] == "error":
            return JsonResponseBadRequest(additional_data={'err_id': article_k8_task_response['err_id']})

        else:
            if not DEBUG:
                # Delegate provisioning task to celery
                celery_check_flyio_provisioning.delay(article_k8_task_response['machine_id'],
                                                      article_k8_task_response['job_id'],
                                                      FLY_ARTICLE_GEN_APP_NAME,
                                                      FLY_ARTICLE_GEN_DEPLOY_TOKEN)

            return JsonResponse(
                status=200,
                data={
                    "message": "OK",
                    "job_id": article_k8_task_response['job_id']
                },
            )

    else:
        return JsonResponseBadRequest()

    return JsonResponse(
        status=200,
        data=dict(
            message="OK"
        ),
        safe=False,
    )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_add_backlink(request: Request):
    """
    API to add Backlink list
    params: Django Request object
    return: list of backlink
    """
    serializer = CSVUploadSerializer(data=request.data)

    if serializer.is_valid():
        csv_file = serializer.validated_data['csv_file']

        # Read and parse the CSV file
        decoded_file = csv_file.read().decode('utf-8').splitlines()
        reader = csv.reader(decoded_file)

        # Skip the header row
        next(reader)

        errors = []

        # Iterate over each row and save the data to the model
        for row in reader:
            try:
                if not BackLink.objects.filter(name=row[0]).exists():
                    backlink = BackLink(
                        name=row[0],
                        url=row[1],
                        da_score=int(row[2]),
                        follow_unfollow_link=row[3],
                        submissions=row[4],
                        article_link=row[5],
                    )
                    backlink.save()

            except Exception as e:
                continue

        backlinks = BackLink.objects.all().order_by('name')
        serializer = BackLinkSerializer(backlinks, many=True)

        return JsonResponse(status=201, data={"backlinks": serializer.data})

    return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_backlinks(_: Request):
    """
    API to get backlinks list
    params: Django Request object
    return: list of backlinks
    """
    backlinks = BackLink.objects.all().order_by('name')
    serializer = BackLinkSerializer(backlinks, many=True)
    return JsonResponse(status=200, data={"backlinks": serializer.data})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_remove_backlinks(request: Request):
    """
    API to remove backlink from backlink list
    params: Django Request object
    return: list of backlinks
    """
    try:
        names: list = request.data['names']
    except KeyError as k:
        logger.error(f"admin_remove_backlinks() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        # remove backlink from model
        BackLink.objects.filter(name__in=names).delete()
    except Exception as e:
        logger.error(f"admin_remove_backlinks() - Error {e}")
        return JsonResponseBadRequest()

    backlinks = BackLink.objects.all().order_by('name')
    serializer = BackLinkSerializer(backlinks, many=True)

    return JsonResponse(status=200,data={"backlinks": serializer.data})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_toggle_backlink_visibility(request: Request):
    """
    API to toggle visibility of backlink to free plan user
    params: Django Request object
    return: list of backlink
    """
    try:
        name: list = request.data['name']
    except KeyError as k:
        logger.error(f"admin_toggle_backlink_visibility() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        # toggle visibility of backlink
        backlink_obj = BackLink.objects.get(name=name)
        backlink_obj.show_on_free_plan = False if backlink_obj.show_on_free_plan else True
        backlink_obj.save()
    except Exception as e:
        logger.error(f"admin_toggle_backlink_visibility() - Error {e}")
        return JsonResponseBadRequest()


    backlinks = BackLink.objects.all().order_by('name')
    serializer = BackLinkSerializer(backlinks, many=True)
    return JsonResponse(status=200,data={"backlinks": serializer.data})


@api_view(["POST"])
@permission_classes([IsAuthenticated])
@admin_only
def admin_add_auto_coupon(request: Request):
    """
    API to add auto apply coupon
    params: Django Request object
    return: list of auto coupons
    """
    data = request.data

    name = data.get("name", "").lstrip().rstrip()
    coupon_id = data.get("couponId", "").lstrip().rstrip()

    if not name or not coupon_id:
        return JsonResponseBadRequest()

    if AutoCoupon.objects.filter(coupon_code=coupon_id).exists():
        return JsonResponse(status=400, data={"message":"coupon_exist"})

    AutoCoupon.objects.create(name=name, coupon_code=coupon_id)
    coupons = AutoCoupon.objects.all().order_by("-created_at")
    serializer = AutoCouponSerializer(coupons, many=True)
    return JsonResponse(status=200, data={"coupons":serializer.data})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_enable_disable_coupon(request: Request):
    """
    API to enable/disable auto coupons
    params: Django Request object
    return: list of backlink
    """
    try:
        coupon_id: list = request.data['couponId']
    except KeyError as k:
        logger.error(f"admin_toggle_backlink_visibility() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        # enable/disable coupon
        coupon_obj = AutoCoupon.objects.get(coupon_code=coupon_id)
        coupon_obj.enabled = False if coupon_obj.enabled else True
        coupon_obj.save()
    except Exception as e:
        logger.error(f"admin_enable_disable_coupon() - Error {e}")
        return JsonResponseBadRequest()

    coupons = AutoCoupon.objects.all().order_by("-created_at")
    serializer = AutoCouponSerializer(coupons, many=True)
    return JsonResponse(status=200,data={"coupons": serializer.data})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_coupons(request):
    """
    API to get auto coupons list
    params: Django Request object
    return: list of coupons
    """
    coupons = AutoCoupon.objects.all().order_by('-created_at')
    serializer = AutoCouponSerializer(coupons, many=True)
    return JsonResponse(status=200, data={"coupons": serializer.data})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_remove_coupons(request: Request):
    """
    API to remove coupons from coupon list
    params: Django Request object
    return: list of coupon
    """
    try:
        coupon_ids: list = request.data['couponIds']
    except KeyError as k:
        logger.error(f"admin_remove_coupons() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        # remove coupons from model
        AutoCoupon.objects.filter(coupon_code__in=coupon_ids).delete()

    except Exception as e:
        logger.error(f"admin_remove_backlinks() - Error {e}")
        return JsonResponseBadRequest()

    coupons = AutoCoupon.objects.all().order_by('-created_at')
    serializer = AutoCouponSerializer(coupons, many=True)

    return JsonResponse(status=200,data={"coupons": serializer.data})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_article_logs(request: Request):
    """
    Returns all articles on abun.
    :param request: Django Rest Framework's Request object
    """

    # Get pagination and filter parameters
    try:
        page = int(request.GET.get('page', 1))
    except ValueError:
        page = 1

    try:
        per_page = int(request.GET.get('per_page', 10))
    except ValueError:
        per_page = 10

    search = request.GET.get('search', '')

    # Use select_related to fetch related data in a single query
    base_queryset = Article.objects.filter(is_generated=True) \
        .select_related('keyword', 'website', 'website__user') \
        .order_by('-created_on')

    # Apply search if provided
    if search:
        search_query = Q(title__icontains=search) | \
            Q(website__user__email__icontains=search) | \
            Q(keyword__keyword__icontains=search)
        base_queryset = base_queryset.filter(search_query)

    # Get total count and paginate
    paginator = Paginator(base_queryset, per_page)

    try:
        paginated_articles = paginator.page(page)
    except (EmptyPage, InvalidPage):
        paginated_articles = paginator.page(paginator.num_pages)

    # Serialize the article data
    serializer = ArticleSerializer(paginated_articles, many=True)

    # Format the response data
    response_data = {
        'articles': serializer.data,
        'total': paginator.count
    }

    return JsonResponse(status=200, data=response_data, safe=False)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_user_integrations(request):
    """
    API to get user integration data
    params: Django Request object
    return: list of backlinks
    """
    try:
        user_id: int = int(request.query_params['user_id'])
    except KeyError as k:
        logger.error(f"admin_get_user_integrations() - Missing key {k}")
        return JsonResponseBadRequest()

    try:
        user: User = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"admin_get_user_integrations() - No User with id {user_id}")
        return JsonResponseBadRequest()

    webflow_serializer = WebflowSitesSerializer(user.webflow_integrations.all(), many=True)
    wordpress_serializer = WordpressSitesSerializer(user.wordpress_integrations.all(), many=True)
    wix_serializer = WixSitesSerializer(user.wix_integrations.all(), many=True)
    shopify_serializer = ShopifyShopsSerializer(user.shopify_integrations.all(), many=True)
    ghost_serializer = GhostSitesSerializer(user.ghost_integrations.all(), many=True)
    ghl_serializer = GHLSitesSerializer(user.ghl_integrations.all(), many=True)    
    gsc_verified_sites  = get_verified_gsc_sites(user)
    gsc_serializer = GoogleSerializer(user.gsc_integrations.all(), many=True, context={"verified_domains": gsc_verified_sites})
        
    integrations = {
        'webflow':{
            'sites' : webflow_serializer.data,
            'count' : len(webflow_serializer.data)
        },
        'wordpress':{
            'sites' : wordpress_serializer.data,
            'count' : len(wordpress_serializer.data)
        },
        'wix':{
            'sites' : wix_serializer.data,
            'count' : len(wix_serializer.data)
        },
        'shopify':{
            'shops' : shopify_serializer.data,
            'count' : len(shopify_serializer.data)
        },
        'ghost':{
            'sites' : ghost_serializer.data,
            'count' : len(ghost_serializer.data)
        },
        'ghl':{
            'sites' : ghl_serializer.data,
            'count' : len(ghl_serializer.data)
        },
        'gsc': {
            'sites' : gsc_serializer.data,            
            'count' : len(gsc_serializer.data)
        }
    }
    return JsonResponse(status=200, data={"integrations": integrations})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
@transaction.atomic
def admin_remove_integrations(request: Request):
    """
    Removes current integration from website.
    :param request: Django Rest Framework's Request object.
    """


    if request.method == 'POST':
        try:
            user_id: int = int(request.data['user_id'])
        except KeyError as k:
            logger.error(f"admin_get_user_integrations() - Missing key {k}")
            return JsonResponseBadRequest()

        try:
            user: User = User.objects.get(id=user_id)
        except User.DoesNotExist:
            logger.error(f"admin_get_user_integrations() - No User with id {user_id}")
            return JsonResponseBadRequest()

        try:
            integration = request.data['integration_type']
            integration_unique_text_id = request.data.get('integration_unique_id')
        except KeyError:
            return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY"})

        if integration in ['wordpress', 'webflow', 'wix', 'shopify','ghost', 'ghl']:
            reset_website_integrations(user, integration, integration_unique_text_id=integration_unique_text_id)
        elif integration in ['google-search-console']:
            reset_google_integration(user, integrated_app=integration)
        else:
            return JsonResponseBadRequest(additional_data={'err_id': "INVALID_INTEGRATION"})

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
@transaction.atomic
def admin_post_changelog(request: Request):
    """
    Adds the Changelogs.
    :param request: Django Rest Framework's Request object.
    """

    try:
        title: str = request.data['title']
        description: str = request.data['description']
        datetime_string = request.data['date']
    except KeyError as k:
        logger.error(f"admin_post_changelog() - Missing key {k}")
        return JsonResponseKeyError()

    try:
        # Convert it to a naive datetime object
        naive_datetime = datetime.datetime.strptime(datetime_string, '%Y-%m-%dT%H:%M')
        # Make the datetime object timezone-aware using Django's timezone support
        formatted_datetime = timezone.make_aware(naive_datetime)

        ChangeLog.objects.create(title=title, description=description, created_at=formatted_datetime)

        return JsonResponse(
            status=200,
            data=ChangeLogSerializer(ChangeLog.objects.all().order_by("-created_at"), many=True).data,
            safe=False,
        )
    except Exception as err:
        logger.error(f"admin_post_changelog() - Unable to create changelog error : {err}")
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
@transaction.atomic
def admin_edit_changelog(request: Request):
    """
    Updates the changelogs.
    :param request: Django Rest Framework's Request object.
    """

    try:
        changelog_id = request.data['id']
        title: str = request.data['title']
        description: str = request.data['description']
        date = request.data['date']

    except KeyError as k:
        logger.error(f"admin_post_changelog() - Missing key {k}")
        return JsonResponseKeyError()

    if ChangeLog.objects.filter(id=changelog_id).exists():
        changelog_obj = ChangeLog.objects.get(id=changelog_id)
        changelog_obj.title = title
        changelog_obj.description = description
        changelog_obj.date = date
        changelog_obj.save()

        return JsonResponse(
            status=200,
            data=ChangeLogSerializer(ChangeLog.objects.all().order_by("-created_at"), many=True).data,
            safe=False,
        )

    return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
@transaction.atomic
def admin_delete_changelog(request: Request):
    """
    Delete Changelogs.
    :param request: Django Rest Framework's Request object.
    """
    try:
        changeLogIds = request.data['changeLogIds']
    except KeyError as k:
        logger.error(f"admin_post_changelog() - Missing key {k}")
        return JsonResponseKeyError()

    if ChangeLog.objects.filter(id__in=changeLogIds).exists():
        ChangeLog.objects.filter(id__in=changeLogIds).delete()

        return JsonResponse(
            status=200,
            data=ChangeLogSerializer(ChangeLog.objects.all().order_by("-created_at"), many=True).data,
            safe=False,
        )

    return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
@transaction.atomic
def admin_retry_stuck_failed_website_scanning_task(request: Request):
    """
    API view to retry stuck/failed website scanning task.
    :param request: Django Rest Framework's Request object.
    """
    try:
        user_id: int = request.data['user_id']
        website_domain: str = request.data['website_domain']
    except KeyError as k:
        logger.error(f"Missing key {k}")
        return JsonResponseKeyError()

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"No user found with {user_id} ID.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_USER_FOUND", 'message': f"No user found with {user_id} ID."})

    try:
        website = Website.objects.get(domain=website_domain)
    except Website.DoesNotExist:
        logger.error(f"No website found with {website_domain} domain.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_FOUND", 'message': f"No website found with {website_domain} domain."})

    if not user.website_set.all().filter(domain=website.domain).exists():
        logger.critical(f"{website.domain} is not connected to {user.email} user account.")
        return JsonResponseBadRequest(additional_data={'err_id': "WEBSITE_IS_NOT_CONNECTED",
                                                       'message': f"{website.domain} is not connected to {user.email} user account."})

    if website.is_crawled or website.task_queued:
        return JsonResponseBadRequest(additional_data={'err_id': "ALREADY_CRAWLED",
                                                       'message': "Website is already crawled."})

    # Mark the website as not crawling
    website.is_crawling = False
    website.is_crawled = False
    website.is_failed = False
    website.save()

    # Delegate the task to celery
    # NOTE: Since this is a retry, we are not passing `request_from_admin` as True to scan webpages as per user's plan
    celery_start_website_scanning.delay(website.domain, tool_to_run='generate-summary')

    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
@transaction.atomic
def admin_scrape_more_website_pages(request: Request):
    """
    Admin API view to scrape more website pages.
    :param request: Django Rest Framework's Request object.
    """
    try:
        user_id: int = request.data['user_id']
        website_domain: str = request.data['website_domain']
    except KeyError as k:
        logger.error(f"Missing key {k}")
        return JsonResponseKeyError()

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"No user found with {user_id} ID.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_USER_FOUND", 'message': f"No user found with {user_id} ID."})

    try:
        website = Website.objects.get(domain=website_domain)
    except Website.DoesNotExist:
        logger.error(f"No website found with {website_domain} domain.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_FOUND", 'message': f"No website found with {website_domain} domain."})

    if not user.website_set.all().filter(domain=website.domain).exists():
        logger.critical(f"{website.domain} is not connected to {user.email} user account.")
        return JsonResponseBadRequest(additional_data={'err_id': "WEBSITE_IS_NOT_CONNECTED",
                                                       'message': f"{website.domain} is not connected to {user.email} user account."})

    if website.is_crawling or website.task_queued:
        return JsonResponseBadRequest(additional_data={'err_id': "PROCESSING",
                                                       'message': "Website scanning task is already processing or in queue."})

    # Mark the website as not crawling
    website.is_crawling = False
    website.is_crawled = False
    website.is_failed = False
    website.save()

    # Delegate the task to celery
    celery_start_website_scanning.delay(website.domain, request_from_admin=True, tool_to_run='generate-summary')

    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_article_generation_api(request: Request):
    """
    Starts article generation for given article title.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            article_uid: str = request.data['article_uid']
        except KeyError:
            return JsonResponseBadRequest()

        article: Article = Article.objects.get(article_uid=article_uid)
        user = article.user
        # check user article generation limit for the month
        current_plan_data: Dict = get_stripe_product_data(user)
        article_limit: int = current_plan_data['metadata']['max_articles']

        if user.articles_generated >= article_limit:
            return JsonResponse(status=200, data={'status': "rejected", 'reason': "max_limit_reached"})

        # ------------------- Create K8 Job -------------------
        article_k8_task_response = create_article_generation_v2_task(user, article_uid)

        if article_k8_task_response['status'] == "error":
            return JsonResponseBadRequest(additional_data={'err_id': article_k8_task_response['err_id'], 'message': article_k8_task_response['message']})

        if not DEBUG:
            # Delegate provisioning task to celery
            celery_check_flyio_provisioning.delay(article_k8_task_response['machine_id'],
                                                  article_k8_task_response['job_id'],
                                                  FLY_ARTICLE_GEN_APP_NAME,
                                                  FLY_ARTICLE_GEN_DEPLOY_TOKEN)

        articles: QuerySet[Article] = user.articles.select_related(
            'keyword',
            'schedulearticleposting'
        ).only(
            'article_uid',
            'title',
            'internal_link_count',
            'external_link_count',
            'image_count',
            'word_count',
            'is_processing',
            'is_generated',
            'is_posted',
            'is_failed',
            'is_archived',
            'is_user_added',
            'article_link',
            'posted_to',
            'created_on',
            'generated_on',
            'posted_on',
            'feedback',
            'keyword__keyword',
            'keyword__keyword_md5_hash',
            'keyword__volume',
            'schedulearticleposting__schedule_on'
        ).prefetch_related(
            Prefetch('keyword__keywordproject_set',
                    queryset=KeywordProject.objects.only('project_id'))
        )

        updated_title_data = ArticleTitleTableDataSerializer(articles, many=True).data

        user.articles_generated += 1
        user.total_articles_generated += 1
        user.save()

        return JsonResponse(status=200, data={
            'status': "sent_for_processing",
            'updated_title_data': updated_title_data,
        })

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_post_article_api(request: Request):
    """
    Posts article to user's website based on integration settings. If user has not set up any integration, returns
    bad request (status code 400).

    :param request: Django Rest Framework's Request object.
    """

    if request.method == 'POST':
        try:
            article_uid: str = request.data['articleUID']
            selected_integration: str = request.data['selectedIntegration']
            selected_integration_unique_text_id: str = request.data.get('selectedIntegrationUniqueID')
            post_status: Literal['draft', 'publish'] = request.data.get('post_status', 'publish')
            selected_categories = request.data.get('selected_categories')
            update_published_article = request.data.get('update_published_article', False)
        except KeyError:
            return JsonResponseBadRequest()

        try:
            article: Article = Article.objects.get(article_uid=article_uid)
            user = article.user
        except Article.DoesNotExist:
            logger.error(f"No such article exists (article uid: {article_uid})")
            return JsonResponseBadRequest()

        if selected_integration not in user.all_integrations:
            logger.error(f"'{user.email}' selected integration '{selected_integration}' is not supported.")
            return JsonResponseBadRequest()

        if "wordpress" in selected_integration and user.wordpress_integrations.exists():
            res = publish_article_to_wp(article,
                                        user,
                                        wp_site_url=selected_integration_unique_text_id,
                                        status=post_status,
                                        selected_categories=selected_categories,
                                        update_published_article=update_published_article)

            if res["status"] == "success":
                logger.debug(f"Article {article.article_uid} posted successfully!")
                return JsonResponse(status=200, data={
                    'link': res["article_link"],
                    'article_uid': article.article_uid,
                    'posted_to': article.posted_to,
                    'posted_on': article.posted_on,
                })

            else:
                logger.error(f"Article posting failed. Error message: {res['error_message']}")
                return JsonResponseServerError()

        elif "webflow" in selected_integration and user.webflow_integrations.exists():
            # Publish the article to webflow
            res = publish_article_to_wf(article, user, selected_integration_unique_text_id, status=post_status,
                                        publish_site=post_status == "publish", update_published_article=update_published_article)

            if res and res["status"] == "success":
                return JsonResponse(status=200, data={
                    'link': res["article_link"],
                    'article_uid': article_uid,
                    'posted_to': article.posted_to,
                    'posted_on': article.posted_on,
                })

            else:
                if res and res.get("error_message"):
                    logger.error(res["error_message"])
                else:
                    logger.error("Article posting failed due to unknown reason from webflow.")

                return JsonResponseServerError()

        elif "wix" in selected_integration and user.wix_integrations.exists():
            # Publish the article to wix
            response: Dict = publish_article_to_wix(article, site_id=selected_integration_unique_text_id, status=post_status, update_published_article=update_published_article)

            if not response["status"] == "success":
                logger.error("Article posting failed due to unknown reason from wix.")
                logger.error(response['error_message'])
                return JsonResponseServerError()

            return JsonResponse(status=200, data={
                'link': response["article_link"],
                'article_uid': article_uid,
                'posted_to': article.posted_to,
                'posted_on': article.posted_on,
            })

        elif "shopify" in selected_integration and user.shopify_integrations.exists():
            # Publish the article to shopify
            response: Dict = publish_article_to_shopify(article, status=post_status, shop_url=selected_integration_unique_text_id, update_published_article=update_published_article)

            if not response["status"] == "success":
                logger.error("Article posting failed due to unknown reason from shopify.")
                logger.error(response['error_message'])
                return JsonResponseServerError()

            return JsonResponse(status=200, data={
                'link': response["article_link"],
                'article_uid': article_uid,
                'posted_to': article.posted_to,
                'posted_on': article.posted_on,
            })

        elif "ghost" in selected_integration and user.ghost_integrations.exists():
            status_for_ghost = "published" if post_status == "publish" else post_status
            # Publish the article to ghost
            response: Dict = publish_article_to_ghost(article, site_url=selected_integration_unique_text_id, status=status_for_ghost, update_published_article=update_published_article)

            if not response["status"] == "success":
                logger.critical(f"Article posting failed due to unknown reason from ghost cms.")
                logger.error(response['error_message'])
                return JsonResponseServerError()

            return JsonResponse(status=200, data={
                'link': response["article_link"],
                'article_uid': article_uid,
                'posted_to': article.posted_to,
                'posted_on': article.posted_on,
            })

        else:
            logger.critical(f"Could not post article to user's website due to bad integration")
            return JsonResponseServerError()

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_generate_new_featured_image(request: Request):
    """
    API to generate new featured image for user
    params: Django Request object
    return: new featured image url
    """

    try:
        article_uid: str = request.data['article_uid']
    except KeyError:
        return JsonResponseBadRequest()

    try:
        article: Article = Article.objects.get(article_uid=article_uid)
        user = article.user
    except Article.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_ARTICLE"})

    if not user.feature_image_required:
        return JsonResponse(status=200, data={'title': article.title, 'featured_image': ""})


    # delete existing featured image for the article
    if article.selected_featured_image:
        # remove all featured images for the article
        current_template_image_url = article.selected_featured_image.template_image_url
        article.selected_featured_image = None
        article.featured_images.clear()
        article.save()
    else:
        current_template_image_url = ""

    try:
        if "-text" in user.feature_image_template_id:
            template_photo_url = "https://cdn.abun.com/abun-media%2Fdefault-featured-image.jpeg"
            ai_image_response: Dict = generate_AI_feature_image__sync(article_uid,
                                                                      user.feature_image_template_label == "premium" and "segmind" or "deepinfra")
            save_featured_image(ai_image_response["image_url"], "ai_image_generation",
                                article, user.feature_image_template_id, template_photo_url)

        else:
            # Prompt gpt to make the title shorter
            article_title: str = rephrase_article_title(article)

            # User selected template for featured image
            selected_template = user.feature_image_template_id

            # RE-Generate featured image from unsplash
            random_image_count = random.choice(range(1,10))
            template_photo_list = get_unsplash_images(article_title, random_image_count)

            if len(template_photo_list) == (random_image_count-1):
                template_photo = template_photo_list[random.choice(range(random_image_count))]["url"]
            elif template_photo_list:
                template_photo = template_photo_list[0]["url"]
            else:
                template_photo = ""

            if template_photo:
                # RE-Generate featured image
                own_image_response: Dict = generate_custom_feature_image(
                    template_photo,
                    user.current_active_website.logo_url if user.show_logo_on_featured_image else "",
                    article_title,
                    article_uid,
                    selected_template,
                    True,
                    user.images_file_format,
                    current_template_image_url
                )

                # Save featured image.
                if own_image_response["image_url"]:
                    save_featured_image(
                        own_image_response["image_url"],
                        "bannerbear",
                        article,
                        selected_template,
                        template_photo,
                    )

                else:
                    logger.error(f"Featured image generation failed for '{article_uid}' article")
                    return JsonResponseGone(additional_data={'err_id': "FEATURED_IMAGE_GENERATION_FAILED"})

    except Exception as err:
        logger.critical(err)
        return JsonResponseGone(additional_data={'err_id': "FEATURED_IMAGE_GENERATION_FAILED"})

    article.save()

    return JsonResponse(status=200, data={
        'title': article.title,
        'featured_image': article.selected_featured_image.image_url if article.selected_featured_image else None
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_article_settings_page_api(request: Request):
    """
    eturns user article settings page data for Article Settings page.

    :param request: Django Rest Framework's Request object.
    """

    try:
        domain = request.GET['domain']
        website: Website = Website.objects.get(domain=domain)
        user: User = website.user
    except KeyError as k:
        logger.error(f"admin_article_settings_page_api() - Missing key {k}")
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY"})
    except User.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_USER_FOUND"})


    if request.method == 'GET':
        current_plan_data: Dict = get_stripe_product_data(user)
        plan_name: str = current_plan_data['name']

        return JsonResponse(status=200, data={

            'page': "article-settings",
            'keyword_strategy': website.keyword_strategy,
            'all_integrations': user.all_integrations,
            'google_search_console_integration': website.google_search_console_integrated,
            'website_title': website.title,
            'website_description': website.description,
            'website_industry': website.industry,
            'website_icp': website.icp_text,
            'username': user.username,
            'email': user.email,
            'tz': user.user_tz,
            'website_domain': website.domain,
            'competitor_domains': list(website.competitor_set.all().values_list('domain', flat=True)) if user.current_active_website is not None else None,
            'competitor_edit_underway': user.kubernetesjob_set.filter(
                job_id__contains='editcompetitors', status='running'
            ).count() > 0,

            'article_tone_of_voice': website.article_tone_of_voice,
            'external_backlinks_preference': website.external_backlinks_preference,
            'article_language_preference': website.article_language_preference,
            'max_internal_backlinks': website.max_internal_backlinks,
            'max_external_backlinks': website.max_external_backlinks,
            'current_plan_name': plan_name,
            'images_file_format': website.images_file_format,
            'feature_image_required': website.feature_image_required,
            'image_source': website.image_source,
            'website_connection_limit': current_plan_data['metadata']['max_websites'],
            'article_context': website.article_context
        })

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_save_website_settings_api(request: Request):
    """
    Saves website settings. Does not handle 'integration' value.

    :param request: Django Rest Framework's Request object
    """
    if request.method == "POST":
        try:
            settings_to_save: List[Dict] = request.data["settingsToSave"]
            domain = request.data['domain']
            website: Website = Website.objects.get(domain=domain)
            user: User = website.user

        except KeyError:
            return JsonResponseBadRequest()

        except Website.DoesNotExist:
            logger.error(f"No website found with {domain} domain.")
            return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_WEBSITE"})

        try:
            for setting in settings_to_save:
                settings_name: str = setting["settingName"]
                new_value: str = setting["settingValue"]

                if settings_name == "keyword_strategy":
                    website.keyword_strategy = new_value

                elif settings_name == "image_source":
                    website.image_source = new_value

                elif settings_name == "feature_image":
                    # Get the user current plan
                    try:
                        current_plan_name = get_stripe_product_data(user)['name']
                    except stripe.error.InvalidRequestError:
                        current_plan_name = 'Trial'

                    # allow user to use premium template if he/she has already selected a premium template
                    if user.feature_image_template_label == "premium":
                        website.feature_image_template_id = new_value

                    else:
                        previous_feature_image_template_id = website.feature_image_template_id
                        website.feature_image_template_id = new_value

                        # Check if user is on "Basic", "LTD", "Trial", "Seed" or "Starter" plan and trying to use premium featured image template
                        if user.feature_image_template_label == "premium" and current_plan_name in ["Basic", "LTD", "Trial", "Seed", "Starter"]:
                            # Update it back to the previously used Featured Image Template ID
                            website.feature_image_template_id = previous_feature_image_template_id

                elif settings_name == "feature_image_required":
                    website.feature_image_required = new_value

                elif settings_name == "article_tone_of_voice":
                    website.article_tone_of_voice = new_value

                elif settings_name == "external_backlinks_preference":
                    if new_value == "off":
                        website.max_external_backlinks = 0
                    website.external_backlinks_preference = new_value

                elif settings_name == "article_language_preference":
                    website.article_language_preference = new_value

                elif settings_name == "max_internal_backlinks":
                    website.max_internal_backlinks = new_value

                elif settings_name == "max_external_backlinks":
                    website.max_external_backlinks = new_value

                elif settings_name == "generate_bannerbear_featured_image":
                    website.generate_bannerbear_featured_image = new_value

                elif settings_name == "ai_generated_image_style":
                    website.ai_generated_image_style = new_value

                elif settings_name == "images_file_format":
                    website.images_file_format = new_value

                elif settings_name == "article_context":
                    website.article_context = new_value

                else:
                    # NOTE: 'integration' is handled in its own api
                    logger.error(
                        f"save_user_settings() - Bad 'settings_name' value {settings_name}"
                    )
                    return JsonResponseBadRequest()

        except KeyError:
            return JsonResponseBadRequest()
        except Exception as err:
            logger.error(f"Error saving website settings: {str(err)}")
            return JsonResponseServerError()

        website.save()

        return JsonResponse(status=200, data={"message": "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_save_context_in_settings(request):
    """
    Save the context for article generation in settings.
    """

    try:
        context = request.data.get('context', '').strip()
        old_context = request.data.get('old_context', '').strip()
        domain = request.data['domain']
        website: Website = Website.objects.get(domain=domain)
        user: User = website.user
    except KeyError as k:
        logger.error(f"Missing key {k}")
        return JsonResponseBadRequest()
    except Website.DoesNotExist:
        logger.error(f"No website found with {domain} domain.")
        return JsonResponseBadRequest()

    if not website:
        return JsonResponse({"error": "No active website found."}, status=400)

    try:
        existing_context = InstructionAndContext.objects.filter(website=website, context=old_context).first()
        logger.info(f"Existing Instruction Context: {existing_context}")

        if existing_context:
            existing_context.context = context
            existing_context.created_on = timezone.now()
            existing_context.save()
            return JsonResponse({"message": "Context updated successfully."}, status=200)

        # If no existing InstructionAndContext, check articles for old context
        article_context = (
            user.articles
            .filter(context=old_context)
            .exclude(context__isnull=True, context="")
            .order_by('-created_on')
            .first()
        )

        if article_context:
            article_context.context = context
            article_context.created_on = timezone.now()
            article_context.save()
        else:
            InstructionAndContext.objects.create(website=website, context=context)

        return JsonResponse({"message": "Context saved successfully."}, status=201)

    except Exception as e:
        logger.error(f"Error saving context: {str(e)}")
        return JsonResponse({"error": "Failed to save context."}, status=500)



@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_latest_contexts(request):
    """
    Get the latest 10 contexts from InstructionAndContext and Article,
    sorted by created_on in descending order, returning only the context values.
    """

    try:
        domain = request.GET['domain']
        website: Website = Website.objects.get(domain=domain)
        user: User = website.user
    except KeyError as k:
        logger.error(f"admin_article_settings_page_api() - Missing key {k}")
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY"})
    except Website.DoesNotExist:
        logger.error(f"No website found with {domain} domain.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_FOUND"})

    instruction_contexts = InstructionAndContext.objects.filter(
        website=website
    ).order_by('-created_on')

    article_contexts = user.articles.filter().exclude(context__isnull=True).exclude(context="").order_by('-created_on')[:10]

    combined_contexts = sorted(
        list(instruction_contexts) + list(article_contexts),
        key=lambda obj: obj.created_on,
        reverse=True
    )

    unique_contexts = list(OrderedDict.fromkeys(obj.context for obj in combined_contexts if obj.context))

    return JsonResponse({"contexts": unique_contexts})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@admin_only
def admin_get_articles_with_feedback(request: Request):
    """
    Fetches articles with feedback (positive or negative).
    """
    try:
        # Get pagination and filter parameters
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 10))
    except ValueError:
        page = 1
        per_page = 10

    # Filter articles with feedback
    articles_with_feedback = Article.objects.filter(feedback__in=['positive', 'negative']).select_related("website", "keyword").values("article_uid", "title", "keyword__keyword", "website__user__email", "website__user__id", "keyword__keyword_md5_hash", "keyword__volume","internal_link_count", "external_link_count", "image_count", "word_count", "is_processing", "is_generated", "is_posted", "is_failed", "is_archived", "article_link", "is_user_added","posted_to", "created_on", "generated_on", "feedback").order_by('-created_on')

    # Apply search if provided
    search = request.GET.get('search', '')
    if search:
        search_query = Q(title__icontains=search) | \
            Q(keyword__keyword__icontains=search) | \
            Q(feedback__icontains=search) | \
            Q(article_link__icontains=search)
        articles_with_feedback = articles_with_feedback.filter(search_query)

    # Apply sorting
    sort = request.GET.get('sort', '')
    if sort:
        sort_fields = []
        for sort_item in sort.split(','):
            field, direction = sort_item.split(':')
            field_mapping = {
                'title': 'title',
                'keyword': 'keyword__keyword',
                'created_on': 'created_on',
                'generated_on': 'generated_on',
                'feedback': 'feedback',
            }
            db_field = field_mapping.get(field, field)
            if direction == 'desc':
                db_field = f'-{db_field}'
            sort_fields.append(db_field)

        if sort_fields:
            articles_with_feedback = articles_with_feedback.order_by(*sort_fields)

    # Paginate the results
    paginator = Paginator(articles_with_feedback, per_page)
    try:
        paginated_articles = paginator.page(page)
    except (EmptyPage, InvalidPage):
        paginated_articles = paginator.page(paginator.num_pages)


    # Format the response data
    response_data = {
            'articles': [{
                "articleUID": article["article_uid"],
                "articleTitle": article["title"],
                "userEmail": article["website__user__email"],
                "userId": article["website__user__id"],
                "keyword": article["keyword__keyword"] if article["keyword__keyword"] else "",
                "keywordHash": article["keyword__keyword_md5_hash"] if article["keyword__keyword_md5_hash"] else "",
                "keywordTraffic": article["keyword__volume"] if article["keyword__volume"] else 0,
                "internalLinks": article["internal_link_count"] if article['internal_link_count'] else 0,
                "externalLinks": article["external_link_count"] if article['external_link_count'] else 0,
                "images": article["image_count"] if article['image_count'] else 0,
                "wordCount": article["word_count"] if article['word_count'] else 0,
                "isProcessing": article["is_processing"],
                "isGenerated": article["is_generated"],
                "isPosted": article["is_posted"],
                "isFailed": article["is_failed"],
                "isArchived": article["is_archived"],
                "postLink": article["article_link"] if article["article_link"] else "",
                "isUserAdded": article["is_user_added"],
                "postedTo": article["posted_to"] if article["posted_to"] else "",
                "createdOn": article["created_on"].astimezone(pytz.timezone('Asia/Kolkata')).strftime('%d{suffix} %b, %y, %I:%M %p').replace('1{suffix}', '1st').replace('2{suffix}', '2nd').replace('3{suffix}', '3rd').replace('{suffix}', 'th') if article["created_on"] else "",
                "generatedOn": article["generated_on"].astimezone(pytz.timezone('Asia/Kolkata')).strftime('%d{suffix} %b, %y, %I:%M %p').replace('1{suffix}', '1st').replace('2{suffix}', '2nd').replace('3{suffix}', '3rd').replace('{suffix}', 'th') if article["generated_on"] else "",
                "feedback": article["feedback"] if article['feedback'] else "",
            } for article in paginated_articles],
            'total': paginator.count
        }

    return JsonResponse(status=200, data=response_data, safe=False)