import logging
import time
from typing import Di<PERSON>, <PERSON>, <PERSON><PERSON>, Optional

from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from googleapiclient.discovery import build, Resource
from googleapiclient.errors import HttpError

from mainapp.models import GoogleIntegration, User, Website

logger = logging.getLogger('abun.google.integration.utils')


def get_google_oauth2_credentials(user: User, integration_type: str) -> Union[None, Credentials]:
    """
    Returns the google oauth2 credentials
    :param website: User model instance
    :param integration_type: Type of integration (google-search-console or google-analytics)
    """
    website: Website = user.current_active_website

    if not website:
        logger.error(f"Current active website is not set for '{user.email}'.")
        return None

    try:
        # Fetch the integration details
        integration = GoogleIntegration.objects.get(website=website, integration_type=integration_type)
    except GoogleIntegration.DoesNotExist:
        logger.error(f"{integration_type} integration not found for '{website.domain}'.")
        return None    

    # Create Credentials instance
    credentials = Credentials(
        token=integration.token,
        refresh_token=integration.refresh_token,
        token_uri=integration.token_uri,
        client_id=integration.client_id,
        client_secret=integration.client_secret,
        scopes=integration.scopes,
    )

    # Check if the credentials have expired, and refresh if necessary
    if credentials.expired:
        credentials.refresh(Request())

        # Store the updated token
        integration.token = credentials.token
        integration.save()

    return credentials


def build_api_service(service_name: str, version: str, credentials: Credentials) -> Resource:
    """
    Builds a service for interacting with an API.
    :param service_name: Service name to build
    :param version: Service version
    :param credentials: Google oauth2 credentials
    :return: Build service
    """
    service = build(service_name, version, credentials=credentials)
    return service


def get_site_name_on_gsc(credentials: Credentials, domain: str) -> Union[None, str]:
    """
    Returns the site name as on the GSC account
    :param credentials: Google oauth2 credentials 
    :param domain: Currently connected website domain
    :return: str
    """
    sites = fetch_sites_connected_to_gsc(credentials)

    try:
        for site in sites['siteEntry']:
            if site['siteUrl'].count(domain) or domain in site['siteUrl']:
                return site['siteUrl']
    except KeyError:
        return None

    return None


def fetch_sites_connected_to_gsc(credentials: Credentials) -> Dict:
    """
    Fetch connected sites on GSC
    :param credentials: Google oauth2 credentials 
    :return: Dict
    """
    search_console = build_api_service('searchconsole', 'v1', credentials=credentials)
    sites: Dict = search_console.sites().list().execute()
    return sites


def fetch_search_performance_analytics_data(credentials: Credentials, website_url: str, start_date: str, end_date: str) -> Dict:
    """
    Fetches the Search Console Performance Analytics data.
    :param credentials: Google oauth2 credentials
    :param website_url: Connected website URL
    :param start_date: Start date in 'YYYY-MM-DD' format
    :param end_date: End date in 'YYYY-MM-DD' format
    :return: Dict
    """
    # Build google search console api service
    search_console = build_api_service('searchconsole', 'v1', credentials=credentials)

    # Create search query
    request = {
        'startDate': start_date,
        'endDate': end_date,
        'dimensions': ['query', 'page', 'device'],
        'metrics': [
            'clicks', 'impressions', 'ctr', 'averagePosition'
        ],
    }

    # Make request to search console API
    response = search_console.searchanalytics().query(siteUrl=website_url, body=request).execute()

    return response


def fetch_search_appearance_data(credentials: Credentials, website_url: str, start_date: str, end_date: str) -> Dict:
    """
    Fetches the Search Console appearance data.
    :param credentials: Google oauth2 credentials
    :param website_url: Connected website URL
    :param start_date: Start date in 'YYYY-MM-DD' format
    :param end_date: End date in 'YYYY-MM-DD' format
    :return: Dict
    """
    # Build google search console api service
    search_console = build_api_service('searchconsole', 'v1', credentials=credentials)

    # Create search query
    request = {
        'startDate': start_date,
        'endDate': end_date,
        'dimensions': ['searchAppearance']
    }

    # Make request to search console API
    response = search_console.searchanalytics().query(siteUrl=website_url, body=request).execute()

    return response


def fetch_url_inspection_details(credentials: Credentials, website_url: str, url_to_inspect: str) -> Tuple[Dict | None, str]:
    """
    Fetches the Search Console URL inspection data.
    :param credentials: Google oauth2 credentials
    :param website_url: Connected website URL
    :param url_to_inspect: Inspected URL
    :return: Dict and url_to_inspect
    """
    # Build google search console api service
    search_console = build_api_service('searchconsole', 'v1', credentials=credentials)

    # Create search query
    request = {
        'inspectionUrl': url_to_inspect,
        'siteUrl': website_url
    }

    try:
        # Make request to search console API
        response = search_console.urlInspection().index().inspect(body=request).execute()
        return response, url_to_inspect

    except HttpError as err:
        status_code = err.resp.status if hasattr(err, 'resp') and hasattr(err.resp, 'status') else None
        reason = err._get_reason() if hasattr(err, '_get_reason') else str(err)

        if status_code == 403:
            if "You do not own this site" in reason or "inspected URL is not part of this property" in reason:
                logger.error(f"Google Search Console 403 - Site ownership issue for URL {url_to_inspect}: {reason}")

            elif "Quota exceeded" in reason:
                logger.error(f"Google Search Console 403 - Quota exceeded for URL {url_to_inspect}: {reason}")

            else:
                logger.error(f"Google Search Console 403 - Forbidden for URL {url_to_inspect}: {reason}")

        elif status_code == 429:
            logger.error(f"Google Search Console 429 - Rate limit exceeded for URL {url_to_inspect}: {reason}")

        elif status_code == 500:
            logger.error(f"Google Search Console 500 - Internal server error for URL {url_to_inspect}: {reason}")

        else:
            logger.critical(f"Google Search Console HttpError {status_code} for URL {url_to_inspect}: {reason}")

        return None, url_to_inspect


def fetch_sitemap_information(credentials: Credentials, website_url: str) -> Dict:
    """
    Fetches the Search Console site map info.
    :param credentials: Google oauth2 credentials
    :param website_url: Connected website URL
    :return: Dict
    """
    # Build google search console api service
    search_console = build_api_service('searchconsole', 'v1', credentials=credentials)

    # Make request to search console API
    response = search_console.sitemaps().list(siteUrl=website_url).execute()

    return response


def get_view_id(credentials: Credentials, website_url: str) -> str:
    """
    Return the view id for the provided website URL.
    :param credentials: Google oauth2 credentials
    :param website_url: Connected website URL
    :return: str
    """
    # Build google analytics api service
    analytics = build_api_service('analytics', 'v3', credentials=credentials)

    # Get a list of accounts the authenticated user has access to
    accounts = analytics.management().accounts().list().execute()

    for account in accounts['items']:
        # Get a list of properties for each account
        properties = analytics.management().webproperties().list(accountId=account['id']).execute()

        for property in properties['items']:
            # Get a list of views (profiles) for each property
            profiles = analytics.management().profiles().list(
                accountId=account['id'],
                webPropertyId=property['id']
            ).execute()

            for profile in profiles['items']:
                # Check if the view's website URL matches the specified URL
                if profile['websiteUrl'] == website_url:
                    return profile['id']

    return None


def build_analyticsdata_service(credentials: Credentials) -> Resource:
    """
    Builds the analytics data service 
    :param credentials: Google oauth2 credentials
    :return Dict:
    """
    # Build google analytics api service
    analyticsdata = build_api_service('analyticsdata', 'v1beta', credentials=credentials)
    return analyticsdata


def fetch_website_traffic(credentials: Credentials, property_uri: str, start_date: str, end_date: str) -> Dict:
    """
    Fetch the website traffic
    :param credentials: Google oauth2 credentials
    :param property_uri: Google analytics property_id
    :param start_date: Start date in 'YYYY-MM-DD' format
    :param end_date: End date in 'YYYY-MM-DD' forma
    :return: Dict
    """
    # Create search query
    request_body = {
        'dateRanges': [{'start_date': start_date, 'end_date': end_date}],
        'metrics': [{'name': 'totalUsers'}],
    }

    # Build analytics data resource
    analyticsdata = build_analyticsdata_service(credentials)

    # Make request to analytics data API
    response = analyticsdata.properties().runReport(body=request_body, property=property_uri).execute()

    return response


def fetch_traffic_source(credentials: Credentials, property_uri: str, start_date: str, end_date: str) -> Dict:
    """
    Fetch the website traffic source
    :param credentials: Google oauth2 credentials
    :param property_uri: Google analytics property_id
    :param start_date: Start date in 'YYYY-MM-DD' format
    :param end_date: End date in 'YYYY-MM-DD' forma
    :return: Dict
    """
    # Create search query
    request_body = {
        'dateRanges': [{'start_date': start_date, 'end_date': end_date}],
        'dimensions': [{'name': 'source'}],
        'metrics': [{'name': 'totalUsers'}],
    }

    # Build analytics data resource
    analyticsdata = build_analyticsdata_service(credentials)

    # Make request to analytics data API
    response = analyticsdata.properties().runReport(body=request_body, property=property_uri).execute()

    return response


def fetch_user_behavior(credentials: Credentials, property_uri: str, start_date: str, end_date: str) -> Dict:
    """
    Fetches the user behavior
    :param credentials: Google oauth2 credentials
    :param property_uri: Google analytics property_id
    :param start_date: Start date in 'YYYY-MM-DD' format
    :param end_date: End date in 'YYYY-MM-DD' forma
    :return: Dict
    """
    # Create search query
    request_body = {
        'property': property_uri,
        'dateRanges': [{'start_date': start_date, 'end_date': end_date}],
        'dimensions': [{'name': 'pagePath'}],
        'metrics': [
            {'name': 'totalUsers'},
            {'name': 'screenPageViews'},
            {'name': 'userEngagementDuration'},
            {'name': 'bounceRate'}
        ],
    }

    # Build analytics data resource
    analyticsdata = build_analyticsdata_service(credentials)

    # Make request to analytics data API
    response = analyticsdata.properties().runReport(body=request_body, property=property_uri).execute()

    return response


# def fetch_site_content(credentials: Credentials, property_uri: str, start_date: str, end_date: str) -> Dict:
#     """
#     Fetches site content
#     :param credentials: Google oauth2 credentials
#     :param property_uri: Google analytics property_id
#     :param start_date: Start date in 'YYYY-MM-DD' format
#     :param end_date: End date in 'YYYY-MM-DD' forma
#     :return: Dict
#     """
#     # Create search query
#     request_body = {
#         'property': property_uri,
#         'dateRanges': [{'start_date': start_date, 'end_date': end_date}],
#         'dimensions': [{'name': 'fullPageUrl'}],
#         'metrics': [
#             {'name': 'screenPageViews'},
#             {'name': 'engagementRate'},
#             # {'name': 'avgPageLoadTime'},
#             {'name': 'entrances'},
#             {'name': 'exits'}
#         ],
#         'orderBys': [{'metric': {'metricName': 'screenPageViews'}, 'desc': True}]
#     }

#     # Build analytics data resource
#     analyticsdata = build_analyticsdata_service(credentials)

#     # Make request to analytics data API
#     response = analyticsdata.properties().runReport(body=request_body, property=property_uri).execute()

#     return response


# def fetch_event_tracking(credentials: Credentials, property_uri: str, start_date: str, end_date: str) -> Dict:
#     """
#     Fetches event tracking
#     :param credentials: Google oauth2 credentials
#     :param property_uri: Google analytics property_id
#     :param start_date: Start date in 'YYYY-MM-DD' format
#     :param end_date: End date in 'YYYY-MM-DD' forma
#     :return: Dict
#     """
#     # Create search query
#     request_body = {
#         'property': property_uri,
#         'dateRanges': [{'start_date': start_date, 'end_date': end_date}],
#         'dimensions': [{'name': 'eventName'}],
#         'metrics': [
#             {'name': 'eventCount'},
#             {'name': 'uniqueEvents'},
#             {'name': 'eventValue'}
#         ],
#     }

#     # Build analytics data resource
#     analyticsdata = build_analyticsdata_service(credentials)

#     # Make request to analytics data API
#     response = analyticsdata.properties().runReport(body=request_body, property=property_uri).execute()

#     return response


# def fetch_conversion_tracking(credentials: Credentials, view_id: str, start_date: str, end_date: str) -> Dict:
#     """
#     Fetches conversion tracking
#     :param credentials: Google oauth2 credentials
#     :param view_id: Google analytics site view id
#     :param start_date: Start date in 'YYYY-MM-DD' format
#     :param end_date: End date in 'YYYY-MM-DD' forma
#     :return: Dict
#     """
#     # Create search query
#     request = {
#         'reportRequests': [
#             {
#                 'viewId': view_id,
#                 'dateRanges': [{'startDate': start_date, 'endDate': end_date}],
#                 'metrics': [
#                     {'expression': 'ga:goalCompletionsAll'},
#                     {'expression': 'ga:transactions'},
#                     {'expression': 'ga:transactionRevenue'}
#                 ],
#                 'dimensions': [
#                     {'name': 'ga:goalCompletionLocation'},
#                     {'name': 'ga:goalPreviousStep1'},
#                     {'name': 'ga:productName'}
#                 ],
#             }
#         ]
#     }

#     # Make request to analytics API
#     response = build_analyticsdata_service(request, credentials)

#     return response


def fetch_views_by_page_title_and_screen_name(credentials: Credentials, property_uri: str, start_date: str, end_date: str) -> Dict:
    """
    Fetches the views by page title & screen name
    :param credentials: Google oauth2 credentials
    :param property_uri: Google analytics property_id
    :param start_date: Start date in 'YYYY-MM-DD' format
    :param end_date: End date in 'YYYY-MM-DD' forma
    :return: Dict
    """
    # Create search query
    request_body = {
        'property': property_uri,
        'dateRanges': [{'start_date': start_date, 'end_date': end_date}],
        'dimensions': [
            {'name': 'pagePath'},
            {'name': 'pageTitle'}
        ],
        'metrics': [
            {'name': 'screenPageViews'},
        ],
    }

    # Build analytics data resource
    analyticsdata = build_analyticsdata_service(credentials)

    # Make request to analytics data API
    response = analyticsdata.properties().runReport(body=request_body, property=property_uri).execute()

    return response

def fetch_gsc_position_for_url(credentials: Credentials, website_url: str, page_url: str, start_date: str, end_date: str) -> Optional[float]:
    """
    Fetches the average GSC position for a specific page URL, with retry logic for 403 errors.
    """
    max_retries = 3
    delay = 2

    for attempt in range(1, max_retries + 1):
        try:            
            search_console = build_api_service('searchconsole', 'v1', credentials=credentials)
            request = {
                'startDate': start_date,
                'endDate': end_date,
                'dimensions': ['page'],
                'dimensionFilterGroups': [{
                    'filters': [{
                        'dimension': 'page',
                        'operator': 'equals',
                        'expression': page_url
                    }]
                }],
                'rowLimit': 1
            }
            response = search_console.searchanalytics().query(siteUrl=website_url, body=request).execute()
                        
            if 'rows' in response and len(response['rows']) > 0:                
                return response['rows'][0]['position']            
            return None

        except HttpError as err:
            # Check for 403 with reason "forbidden"
            if err.resp.status == 403 and 'forbidden' in str(err).lower():
                logger.error(f"403 Forbidden encountered (Attempt {attempt}): {err}")
                if attempt < max_retries:
                    time.sleep(delay)
                    continue
            logger.error(f"Error fetching GSC position for {page_url}: {err}")
            return None
        except Exception as e:
            logger.critical(f"Unexpected error on attempt {attempt} for {page_url}: {e}")
            return None    
    return None

def get_verified_gsc_sites(user) -> list[str]:
    '''Getting the verfied Domain from gsc'''
    credentials = get_google_oauth2_credentials(user, 'google-search-console')
    if credentials is None:
        return []

    try:
        service = build('webmasters', 'v3', credentials=credentials)
        response = service.sites().list().execute()
        site_entries = response.get("siteEntry", [])

        # Only include verified sites
        verified_sites = [
            site['siteUrl']
            for site in site_entries
            if site.get("permissionLevel") != "siteUnverifiedUser"
        ]
        return verified_sites
    except Exception as e:
        logger.error(f"Failed to fetch GSC domains: {e}")
        return []
    