import concurrent
from functools import lru_cache
from io import BytesIO
import json
import logging
import os
import re
import secrets
import string
import uuid
import redis
import hashlib
import time
import random
import datetime
import html2text
from zoneinfo import ZoneInfo
from collections import namedtuple
from concurrent.futures import ThreadPoolExecutor
from email.mime.application import MIMEApplication
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from itertools import zip_longest, islice
from typing import Any, Iterable, NamedTuple, List, Dict, Literal, Optional, Tuple, Callable
from urllib.parse import urlparse
import base64
import html
import jwt
from google.oauth2.credentials import Credentials
from googleapiclient.http import HttpError

import langchain.schema
import praw
import prawcore
from bs4 import BeautifulSoup
from markdownify import markdownify as md
from langchain_openai import ChatOpenAI
from openai import OpenAI
from langchain.output_parsers import PydanticOutputParser, CommaSeparatedListOutputParser
from pydantic import BaseModel, Field
import boto3
import kubernetes.client
import requests
import tldextract
import markdown
import bs4
import yt_dlp
from django.utils.dateparse import parse_datetime
from pydantic import ValidationError
from cryptography.fernet import Fernet
from django.conf import settings
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db import transaction
from django.db.models import QuerySet
from django.urls import reverse
from django.core.files.base import ContentFile
from django.utils import timezone

from langchain.prompts import PromptTemplate
from rest_framework.request import Request
from retry import retry
from urllib3 import Retry, PoolManager, HTTPResponse
from PIL import Image
from dateutil.relativedelta import relativedelta
import pandas as pd
from requests.auth import HTTPBasicAuth

from AbunDRFBackend.settings import (EMAIL_VERIFICATION_ENCRYPTION_KEY, RESET_PASSWORD_LINK_DOMAIN, REDIS_TASK_DATA_DB, REDIS_ART_GEN_EXPIRY,
                                     ABUN_NOTIFICATION_EMAIL, REDIS_MINI_AI_TOOL_DB, REDIS_MINI_AI_TOOL_EXPIRY, SHOPIFY_API_VERSION,
                                     APPSUMO_CLIENT_ID, APPSUMO_CLIENT_SECRET, WP_RETURN_URL_DOMAIN, FLY_API_HOST, FLY_ARTICLE_GEN_APP_NAME,
                                     FLY_ARTICLE_GEN_DEPLOY_TOKEN, FLY_ARTICLE_GEN_IMAGE_URL, DEBUG, PEXELS_API_KEY, SECRET_KEY, K8_JOB_RETRIES,
                                     FLY_AI_CALCULATOR_APP_NAME, FLY_AI_CALCULATOR_DEPLOY_TOKEN, FLY_AI_CALCULATOR_IMAGE_URL, REDIS_GSC_INSIGHTS_DB,
                                     REDIS_GSC_INSIGHTS_EXPIRY, REDDIT_SECRET_KEY, REDDIT_CLIENT_KEY)
from mainapp.chatgpt_prompts import  (get_industry_icp_prompt_text, segmind_ai_img_context_prompt_text,
                                      rephrase_article_title_prompt_text, generate_visual_description_prompt_text, get_programmatic_seo_prompt_text,
                                      get_glossary_words_prompt_text, get_glossary_contents_prompt_text, table_of_content_prompt_text, interlinking_prompt_text)
from mainapp.custom_errors import WebsiteDomainExtractionError, KeywordsEverywhereNoData, KeywordsEverywhereJSONError
from mainapp.email_messages import account_email_verification_email_body
from mainapp.models import (ArticleImage, GhostIntegration, ScheduleArticlePosting, Website, Competitor, Keyword, Article,
                            FeaturedImage, WordpressIntegration, GoogleIntegration, User, WebsiteLogs,
                            WebflowIntegration, KubernetesJob, AutomationProject, WixIntegration, ShopifyIntegration,
                            BlogFinder, GlossaryContent, RedditPostFinderResult, GuestPostFinderQuery, RedditPostFinderQuery,
                            GSCKeywordStatus, GHLIntegration, WordpressPublishedArticle, AICalculator,
                            KubernetesJobLogs, HypestatData, GuestPostFinderResult)
from mainapp.serializers import (ArticleTitleTableDataSerializer, AutomationProjectSerializer)
from mainapp.stripe_utils import get_stripe_product_data
from mainapp.article_title_gen_v2.serper_scraper4_3 import check_blog_link
from mainapp.chroma_db_manager import ChromaDBManager
from mainapp import google_integration_utils
from mainapp.schema_org_context import SCHEMA_ORG_CONTEXT

# ONE_DAY_IN_SECONDS = 1 * 24 * 60 * 60
POD_TTL_AFTER_FINISH = 10 * 60

logger = logging.getLogger('abun.utils')

USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
        "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 10; SM-A505F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
    ]

# =======================================================================================
# ------------------------------------- MISC. UTILS -------------------------------------
# =======================================================================================

class WebsiteDomainData(NamedTuple):
    domain: str
    protocol: str


def get_website_domain_and_protocol(request: Request) -> WebsiteDomainData:
    """
    Fetchs the domain (w/ subdomain) and protocol. In case of any issue, raises WebsiteDomainExtractionError

    :param request: Django Rest Framework's Request object
    :return: website domain and protocol in WebsiteDomainData namedtuple format
    :raises WebsiteDomainExtractionError: if meta keys are missing / value is invalid
    """
    try:
        website_domain: str = request.META['HTTP_ORIGIN'] \
            if 'HTTP_ORIGIN' in request.META \
            else request.META['HTTP_REFERER']
    except KeyError:
        raise WebsiteDomainExtractionError()

    if not website_domain:
        raise WebsiteDomainExtractionError()

    domain: str = urlparse(website_domain).netloc
    protocol: str = urlparse(website_domain).scheme

    # default `protocol` to https in case it's not a valid value
    if protocol not in ['http', 'https']:
        protocol = 'https'

    return WebsiteDomainData(domain=domain, protocol=protocol)


def generate_default_team_name(username: str) -> str:
    """
    Generates default tam name from username

    :param username: user's username
    :returns: team name
    """
    return username.split(" ")[0][:45]


def unescape_amp_char(s: str | List[str]) -> str | List:
    """
    Used to unescape wrongly escaped (&) char (for keyword projects name, keywords & article titles)
    :param s: string
    """
    if not s:
        return s

    unescape_amp = lambda w: w.replace("ampamp", "and") \
        .replace("&amp;", "and").replace("amp;", "and") \
        .replace(" amp ", " and ").replace("&", "and")

    if isinstance(s, List):
        return [unescape_amp(w) for w in s]
    else:
        return unescape_amp(s)


def generate_k8_job_id(job_type: str, domain: str = "", username: str = "") -> str:
    """
    Generates unique job id for use in KubernetesJob model.

    :param job_type: type of task we are running
    :param domain: ex. testing.abun.com or draftss.com
    :returns: unique kubernetes job id
    :raises Exception:
    """
    if not job_type:
        raise Exception("Please provide a non-empty job_type value")

    job_type = job_type.lower()
    hexadecimal_token: str = secrets.token_hex(8)  # nbytes * 2

    if domain:
        # max d_id characters is 63(max characters limit allowed on k8) - len(hexadecimal) - len(job_type)
        d_id_char_limit = 63 - len(job_type) - len(hexadecimal_token) - 2  # subtracting 2 because underscore used in 'job_type' when creating job
        d_id: str = re.sub(r'[^a-zA-Z0-9]', '', domain).lower()[:d_id_char_limit]
    elif username:
        d_id = re.sub(r'[^a-zA-Z0-9]', '', username.replace(" ", "-").replace("%20", "-")).lower()
        d_id = d_id[:63 - len(job_type) - len(hexadecimal_token) - 2]
    else:
        d_id = 'nodomain'

    return f"{job_type}-{d_id}-{hexadecimal_token}"


def keyword_country_data(source: str,
                         volume: int,
                         cpc: dict,
                         paid_difficulty: float,
                         serp_position: int,
                         trend: List[Dict]) -> dict:
    """
    Generates and returns a dictionary with provided data. Use this to maintain the same structure in database.
    """
    return {
        'source': source,
        'volume': volume,
        'cpc': cpc,
        'paid_difficulty': paid_difficulty,
        'serp_position': serp_position,
        'trend': trend,
    }


def cluster_load_over_limit(core_api_v1: kubernetes.client.CoreV1Api,
                            max_pods_per_node: int,
                            cluster_load_threshold: int) -> bool:
    """
    Calculates current cluster load using current pod count and number of nodes in cluster. Returns True if
    load % is greater than threshold.

    :param core_api_v1: CoreV1Api object.
    """
    node_list = core_api_v1.list_node()
    pod_list = core_api_v1.list_pod_for_all_namespaces()
    cluster_load: float = round(
        (len(pod_list.items) / (len(node_list.items) * max_pods_per_node)) * 100.0, 2
    )
    logger.info(f"[*] Cluster Load: {cluster_load}%")
    return cluster_load > cluster_load_threshold

# =========================================================================================
# ------------------------------------- WEBHOOK UTILS -------------------------------------
# =========================================================================================

class WebhookRequestError(Exception):
    def __init__(self, msg, url):
        self.url = url
        super(WebhookRequestError, self).__init__(msg)


class WebhookEventCodes:
    # content generation
    content_generation_done = 'content_plan_generation_completed'
    content_generation_failed = 'content_plan_generation_failed'
    # add keywords
    add_keywords_done = 'add_keywords_completed'
    add_keywords_failed = 'add_keywords_failed'
    add_keywords_bad_keywords = 'add_keywords_bad_keywords'
    # logo color
    logo_color_done = 'logo_color_completed'
    logo_color_failed = 'logo_color_failed'


def make_webhook_request(wh_url: str, event_type: str, timestamp: int, **kwargs):
    """
    Creates a POST request to provided webhook url `wh_url`.

    `event_type` and `timestamp` are required values. Any additional keyword arguments provided are sent in request body

    :param wh_url: Webhook URL
    :param event_type: One of the WebhookEventCodes class member variable values
    :param timestamp: UTC unix timestamp
    :param kwargs: Additional payload data
    :raises WebhookRequestError:
    """
    headers = {
        'Content-Type': 'application/json'
    }
    payload = {**{'event': event_type, 'timestamp': timestamp}, **kwargs}
    try:
        retries = Retry(connect=5,
                        status=5,
                        status_forcelist=[502, 503],
                        raise_on_status=False,
                        backoff_factor=1)
        http = PoolManager(retries=retries)
        res: HTTPResponse = http.request('POST', url=wh_url, body=json.dumps(payload).encode('utf-8'), headers=headers)
        if res.status != 200:
            raise WebhookRequestError(msg=f"Webhook request failed with status code {res.status} after "
                                          f"exhausting all retries",
                                      url=wh_url)
    except Exception as err:
        WebhookRequestError(msg=err, url=wh_url)


# ============================================================================================
# ------------------------------------- KUBERNETES UTILS -------------------------------------
# ============================================================================================

ImageDetails = namedtuple('ImageDetails', 'image, script_name')


def get_image_details(job_category: Literal['content_plan']) -> ImageDetails:
    """
    Returns image name/url & the entrypoint script file name based on provided job_category.

    :param job_category:
    :return:
    """
    # job category - image details mapping
    # these should match the files in AbunK8 project
    _mapping = {
        'content_plan': ('abun-content-plan-generation', 'content_plan_generation.py'),
        'add_competitor': ('abun-add-competitor', 'add_competitor.py'),
        'bulk_add_keywords': ('abun-bulk-add-keywords', 'bulk_add_keywords.py'),
        'logo_color': ('abun-logo-color', 'logo_color.py'),
        'article_generation': ('abun-article-generation', 'article_generation.py'),
        'article_generation_v2': ('abun-article-generation-v2', 'article_generation_V2.py'),
        'article_generation_v3': (('abun-article-gen', 'abun-article-gen-staging'), 'article_gen.py'),
        'edit_competitors': ('abun-edit-competitors', 'edit_competitors.py'),
        'website_scanning': ('abun-website-scanning', 'batch_webpage_scraper.py'),
        'article_internal_link': (('abun-article-internal-linking', 'abun-article-internal-linking-staging'), 'article_internal_link_generation.py'),
        'competitor_finder': ('abun-competitor-finder-v2','competitor_finder.py'),
        'ai_calculator_generation': ('abun-ai-calculator-generation', 'ai_calculator_processor.py')
    }
    image_name_or_names, script_name = _mapping[job_category]
    image_tag: str = os.environ['K8_IMAGE_TAG']

    if isinstance(image_name_or_names, tuple):
        image_name = image_name_or_names[1] if os.environ['K8_IMAGE_TAG'] == 'staging' else image_name_or_names[0]
    else:
        image_name = image_name_or_names

    # since we are using staging k8 cluster for development too, we'll have to save the images on remote repo to be
    # able to use it.
    return ImageDetails(
        f"registry.gitlab.com/aminmemon/abunk8/{image_name}:{image_tag}",
        script_name
    )


def create_k8_job(job_name: str,
                  job_category: Literal[
                      'content_plan',
                      'add_competitor',
                      'bulk_add_keywords',
                      'logo_color',
                      'article_generation',
                      'article_generation_v2',
                      'article_generation_v3',
                      'edit_competitors',
                      'website_scanning',
                      'article_internal_link',
                      'competitor_finder',
                      'ai_calculator_generation'
                      ],
                  container_name: str,
                  user_id: int,
                  additional_args: list[str],
                  retry_counter: int = 3,
                  delay: int = 5,
                  max_pod_per_node: int = 110,
                  cluster_load_threshold: int = 75) -> bool:
    """
    Creates a kubernetes Job resource with provided details. Raises exeption in case of wrong `job_category` or
    some issue with creating the Job on cluster

    :param job_name: Will be assigned to job metadata.
    :param job_category: Allowed values are mentioned in the variable type.
    :param container_name: Will be assigned to container metadata.
    :param user_id: Database id (primary key) of this user.
    :param additional_args: List of additional arguments to pass to the python call.
    :param retry_counter: Number of times to retry if there are no resources available to run the job.
    :param delay: Delay in retrying the request to k8s cluster (in seconds).
    :param max_pod_per_node: max pod per node count
    :param cluster_load_threshold: cluster load threshold limit
    :return Boolean.
    :raises kubernetes.client.ApiException:
    :raises Exception:
    """
    if job_category not in [
        'content_plan',
        'add_competitor',
        'bulk_add_keywords',
        'logo_color',
        'article_generation',
        'article_generation_v2',
        'article_generation_v3',
        'edit_competitors',
        'website_scanning',
        'article_internal_link',
        'competitor_finder',
        'ai_calculator_generation'
    ]:
        raise Exception(f"Bad `job_category` value ({job_category}) provided.")

    with kubernetes.client.ApiClient() as api_client:
        v1 = kubernetes.client.CoreV1Api(api_client)

        # If calculated cluster load < CLUSTER_LOAD_THRESHOLD, create the Job and fetch next message.
        # Otherwise, wait using while loop with 5 sec sleeps until cluster load < CLUSTER_LOAD_THRESHOLD.
        while cluster_load_over_limit(v1, max_pod_per_node, cluster_load_threshold):
            retry_counter -= 1
            logger.info(f"[*] Cluster busy. Waiting for {delay} seconds...")
            time.sleep(delay)

            if retry_counter <= 0:
                return False

    with kubernetes.client.ApiClient() as api_client:
        batch_v1_api = kubernetes.client.BatchV1Api(api_client)

        namespace: str = os.environ['K8_NAMESPACE']
        image_details: ImageDetails = get_image_details(job_category)

        # job main body
        body = kubernetes.client.V1Job(api_version="batch/v1", kind="Job")

        # metadata
        body.metadata = kubernetes.client.V1ObjectMeta(namespace=namespace, name=job_name,
                                                       labels={'category': job_category, 'user_id': str(user_id)})

        # status
        body.status = kubernetes.client.V1JobStatus()

        # job template
        env_variables_secrets_source = kubernetes.client.V1SecretEnvSource(name='abun-k8-env-variables')
        secrets_to_load = [kubernetes.client.V1EnvFromSource(secret_ref=env_variables_secrets_source)]
        containers = [
            kubernetes.client.V1Container(name=container_name,
                                          image=image_details.image,
                                          image_pull_policy='Always',
                                          command=['python', image_details.script_name, *additional_args],
                                          env_from=secrets_to_load)
        ]
        image_pull_secrets = [
            kubernetes.client.V1LocalObjectReference(name='gitlab-registry-credentials')
        ]
        pod_spec = kubernetes.client.V1PodSpec(containers=containers,
                                               image_pull_secrets=image_pull_secrets,
                                               restart_policy='OnFailure')
        template = kubernetes.client.V1PodTemplateSpec(spec=pod_spec)

        # job body spec
        body.spec = kubernetes.client.V1JobSpec(ttl_seconds_after_finished=POD_TTL_AFTER_FINISH,
                                                backoff_limit=settings.K8_JOB_RETRIES,
                                                template=template)

        batch_v1_api.create_namespaced_job(namespace, body)

    return True


def add_keywords(competitor_obj: Competitor, keywords_data: List[Dict]) -> List[Keyword]:
    """
    Bulk creates keyword objects and adds them to `competitor_obj` model ManyToMany related.

    Required Keywords Data:
    -----------------------
    md5_hash
    keyword
    source
    country
    serp_position
    volume
    cpc currency
    cpc value
    paid_difficulty
    trend

    :param competitor_obj: Competitor model object to connect these keywords with.
    :param keywords_data: List of dictionary containing all data (keyword, source, country etc.) for keywords.
    """
    bulk_create_operations = []
    for keyword in keywords_data:
        # remove special characters from keyword
        cleaned_keyword: str = re.sub(r'[^a-zA-Z0-9\s]', "", keyword['keyword'])

        if len(cleaned_keyword) <= 300:
            bulk_create_operations.append(Keyword(
                keyword_md5_hash=keyword['md5_hash'],
                keyword=unescape_amp_char(cleaned_keyword),
                source=keyword['source'],
                country=keyword['country'],
                serp_position=keyword['serp_position'],
                volume=keyword['volume'],
                cpc_currency=keyword['cpc']['currency'],
                cpc_value=keyword['cpc']['value'],
                paid_difficulty=keyword['paid_difficulty'],
                trend={'trends': keyword['trend']},
            ))
        else:
            logger.debug(cleaned_keyword)
            logger.error(f"Keyword length exceeds 300 characters limit")

    keyword_objects: list = Keyword.objects.bulk_create(bulk_create_operations, ignore_conflicts=True)
    competitor_obj.keywords.add(*keyword_objects)

    return keyword_objects


def add_existing_keywords(competitor_obj: Competitor, keywords: List[str], country: str, source: str) -> QuerySet[Keyword]:
    """
    Fetches and adds provided keywords (w/ matching country & source) to the `competitor` object.
    In case any of the provided keyword is not found, they will be ignored. If none of the keywords were found, no
    keywords will be added.

    :param competitor_obj: Competitor model object to connect these keywords with.
    :param keywords: List of keywords (ex. ['kw1', 'kw2', ...]).
    :param country: Used in filter (ex. 'global').
    :param source: Used in filter (ex. 'keywordseverywhere').
    """
    keyword_objects = Keyword.objects.filter(source=source, country=country, keyword__in=keywords)
    competitor_obj.keywords.add(*keyword_objects)

    return keyword_objects


def add_article_titles(titles: List[Dict], connected_website: Website):
    """
    Adds article titles to given website (Creates new Atricle model objects).

    :param titles: List of titles.
    :param connected_website: Website to associate the article titles with.
    """
    bulk_create_operations = []
    for title in titles:
        title_text: str = unescape_amp_char(title['title'])
        title_keyword: str = title['keyword']
        try:
            keyword = Keyword.objects.get(keyword=title_keyword)
        except Keyword.DoesNotExist:
            # If in any case we couldn't fetch the keyword (maybe chatgpt misspelled?)
            # then we set keyword as NULL. On frontend side we can show "N/A", "---"
            # or something similar.
            keyword = None
        bulk_create_operations.append(Article(
            user=connected_website.user,
            website=connected_website,
            title=title_text,
            keyword=keyword
        ))
    Article.objects.bulk_create(bulk_create_operations)


def get_competitor_logo_url(domain: str):
    """
    Returns clearbit logo url for domain.

    :param domain: website domain (ex. draftss.com)
    """
    return f"https://logo.clearbit.com/{domain}"


def generate_title_gen_kw_uid(keyword: str, website_domain: str) -> str:
    """
    Used to generate uid field value for TitleGenKeywords model.

    :param keyword: keyword string value.
    :param website_domain: connected website domain.
    """
    keyword = keyword.strip().replace(' ', '')
    website_domain = website_domain.replace('.', '')
    unique_tail = str(uuid.uuid4())[:8]
    return f"{keyword}-{website_domain}-{unique_tail}"


def generate_title_gen_kw_alt_uid(keyword: str, website_domain: str) -> str:
    """
    Used to generate uid field value for TitleGenKeywordsAlt model.

    :param keyword: keyword string value.
    :param website_domain: connected website domain.
    """
    keyword = keyword.strip().replace(' ', '')
    website_domain = website_domain.replace('.', '')
    unique_tail = str(uuid.uuid4())[:8]
    return f"{keyword}-{website_domain}-alt-{unique_tail}"


def grouper(iterable, n, *, incomplete='fill', fillvalue=None):
    """
    Collect data into non-overlapping fixed-length chunks or blocks

    **Examples:**

    grouper('ABCDEFG', 3, fillvalue='x') --> ABC DEF Gxx

    grouper('ABCDEFG', 3, incomplete='strict') --> ABC DEF ValueError

    grouper('ABCDEFG', 3, incomplete='ignore') --> ABC DEF

    :param iterable: string, list or any other iterable object.
    :param n: chunk size.
    :param incomplete: 'fill', 'strict' or 'ignore'. Default is 'fill'.
    :param fillvalue: value used when 'incomplete' is set to 'fill'. Default is None.
    :raises ValueError: If 'incomplete' has invalid value.
    """
    args = [iter(iterable)] * n
    if incomplete == 'fill':
        return zip_longest(*args, fillvalue=fillvalue)
    if incomplete == 'strict':
        return zip(*args, strict=True)
    if incomplete == 'ignore':
        return zip(*args)
    else:
        raise ValueError('Expected fill, strict, or ignore')


def batched(iterable: Iterable, n: int):
    """
    Generator that returns the iterable in batches of 'n'

    :param iterable: python iterable object.
    :param n: batch size.
    :return: iterable values in batches of size 'n'
    """
    if n < 1:
        raise ValueError('n must be at least one')
    it = iter(iterable)
    while batch := tuple(islice(it, n)):
        yield batch


def generate_article_uid(username: str="noUser", article_type="default") -> str:
    """
    Used to generate uid field value for Article model.

    :param website_domain: connected website domain.
    :param article_type: eg. 'default' or 'how_to'
    """
    unique_tail = str(uuid.uuid4())[:8]
    username = username.replace(" ", "-").replace("%20", "-")
    if article_type == 'default':
        return f"article-{username}-{unique_tail}"
    elif article_type == 'how_to':
        return f"how-to-article-{username}-{unique_tail}"
    elif article_type == 'listicle':
        return f"listicle-{username}-{unique_tail}"
    else:
        raise Exception(f"Invalid article_type value provided ({article_type})")


def get_unsplash_images(search_term: str, count=1, retry_count=3) -> List[Dict]:
    """
    :param search_term: Unsplash image search term.
    :param count: number of images to fetch
    :returns: image details as list of dictionary {url, artist_name, artist_link, download_location}
    """
    try:
        images: List[Dict] = []
        unsplash_client_id = "*******************************************"  # no need to hide this
        unsplash_resp = requests.get(
            f"https://api.unsplash.com/search/photos/"
            f"?query={search_term}"
            f"&page=1"
            f"&per_page={count}"
            f"&client_id={unsplash_client_id}"
        )

        unsplash_data: Dict = json.loads(unsplash_resp.content)

        for result in unsplash_data['results']:
            images.append({
                'url': result['urls']['regular'],
                'artist_name': result['user']['name'],
                'artist_link': result['user']['links']['html'],
                'download_location': result['links']['download_location'],
                'alt': result.get('alt_description', ''),
            })

        return images
    except Exception as err:
        logger.error(f"Unsplash API error: {err}")
        if retry_count > 0:
            return get_unsplash_images(search_term, count, retry_count - 1)

        return [{
                'url': '',
                'artist_name': '',
                'artist_link': '',
                'download_location': '',
            }]


def get_pexels_images(query: str, per_page: int=10):
    """
    Fetch images from Pexels based on a specific query.

    :param query: Search term for images
    :param per_page: Number of images to retrieve (default is 10)
    :return: List of image URLs
    """
    url = "https://api.pexels.com/v1/search"
    headers = {"Authorization": PEXELS_API_KEY}
    params = {"query": query, "per_page": per_page}

    response = requests.get(url, headers=headers, params=params)

    if response.status_code == 200:
        data = response.json()
        logger.info(f"data {data}")
        return [{"url": photo["src"]["original"], "alt": photo["alt"]} for photo in data.get("photos", [])]
    else:
        logger.error(f"Error in get_pexels_images() : {response.status_code} - {response.text}")
        return []


@transaction.atomic
def reset_website_integrations(user: User, integration: str, **kwargs):
    """
    Deletes all active integration on this user.

    :param website: Website model object.
    """
    if integration == "wordpress":
        site_url = kwargs.get("integration_unique_text_id")

        if not site_url:
            logger.error("Wordpress site URL is not provided.")

        else:
            wordpress_integration = user.wordpress_integrations.filter(site_url=site_url)
            wordpress_integration.delete()

    elif integration == "webflow":
        collection_id = kwargs.get("integration_unique_text_id")

        if not collection_id:
            logger.error("Webflow collection ID is not provided.")

        else:
            webflow_integration = user.webflow_integrations.filter(collection_id=collection_id)
            webflow_integration.delete()

    elif integration == 'wix':
        site_id = kwargs.get("integration_unique_text_id")

        if not site_id:
            logger.error("Wix Site ID is not provided.")

        else:
            wix_integration = user.wix_integrations.filter(site_id=site_id)
            wix_integration.delete()

    elif integration == 'shopify':
        shop_url = kwargs.get("integration_unique_text_id")

        if not shop_url:
            logger.error("Shopify Shop URL is not provided.")

        else:
            shopify_integration = user.shopify_integrations.filter(shop_url=shop_url)
            shopify_integration.delete()

    elif integration == "ghost":
        site_url = kwargs.get("integration_unique_text_id")

        if not site_url:
            logger.error("Ghost site URL is not provided.")

        else:
            ghost_integration = user.ghost_integrations.filter(site_url=site_url)
            ghost_integration.delete()

    elif integration == "ghl":
        site_id = kwargs.get("integration_unique_text_id")

        if not site_id:
            logger.error("ghl site URL is not provided.")

        else:
            ghl_integration = user.ghl_integrations.filter(site_id=site_id)
            ghl_integration.delete()
    else:
        logger.error(f"Invalid integration type '{integration}'.")

    # no integration available
    if not user.all_integrations:
        # delete all scheduled article & pause automation projects
        website: Website = user.current_active_website
        ScheduleArticlePosting.objects.all().filter(article__website=website).delete()
        AutomationProject.objects.all().filter(website=website, auto_publish_state="on").update(auto_publish_state="paused")


@transaction.atomic
def reset_google_integration(user: User, integrated_app: str):
    """
    Deletes google search console integration.

    :param user: User model object.
    :param integrated_app: Google integrated app name
    """
    website: Website = user.current_active_website

    # Fetch intgration details
    try:
        integration = GoogleIntegration.objects.get(website=website, integration_type=integrated_app)
    except GoogleIntegration.DoesNotExist:
        logger.error(f"No '{integrated_app}' integration found for '{website.domain}'.")
        return None

    # Make a post request to google api to revoke access
    url = 'https://oauth2.googleapis.com/revoke'
    res = requests.post(url, params={'token': integration.token}, headers={'content-type': "application/x-www-form-urlencoded"})
    response_data = res.json()
    integration_removed = False

    # Success
    if res.ok:
        integration_removed = True

    # Token expired or revoked
    elif response_data['error'] == "invalid_token":
        integration_removed = True

    # Unknown error
    else:
        logger.critical(f"Error while revoking google search console integration for '{website.domain}'.")
        logger.error(res.json())

    if integration_removed:
        # Delete integration
        integration.delete()


@transaction.atomic
def add_wordpress_integration(user: User, site_url: str, user_login: str, password: str):
    """
    Saves Wordpress integration details to user's connected website. This will also delete any existing
    cms integration data for this website.

    :param website: Website model object. Integration will be added to this website.
    :param site_url: Wordpress site url (ex. https://testing.abun.com/)
    :param user_login: "user_login" value received in success url after user approved application connection.
    :param password: "password" value received in success url after user approved application connection.
    """
    # Create the new integration
    wp_integration = WordpressIntegration(
        website=user.current_active_website,
        site_url=site_url,
        user_login=user_login,
        password=password,
    )
    wp_integration.save()


@transaction.atomic
def add_shopify_integration(user: User, shop_url: str, access_token: str):
    """
    Saves Shopify integration details.

    :param user: User model object. Integration will be added to this website.
    :param shop_url: Shopify shop url (ex. https://example.myshopify.com/)
    :param access_token: "access_token" shopify shop access token.
    """
    # Create the new integration
    shopify_integration = ShopifyIntegration(
        website=user.current_active_website,
        shop_url=shop_url,
        access_token=access_token,
    )
    shopify_integration.save()


def save_featured_image(image_url: str, image_source: Literal['defaultimage', 'bannerbear',
                                                              'ai_image_generation', 'uploaded_image'],
                        article: Article, template_id: str, template_image_url: str):
    """
    Creates FeaturedImage and adds it to given article

    :param image_url: Bannerbear image URL.
    :param image_source: Source of the image.
    :param article: Article object
    :param template_id: Bannerbear template id
    :param template_image_url: Template imbe URL
    :return:
    """
    featured_image = FeaturedImage(
        image_url=image_url,
        source=image_source,
        template_id_used=template_id,
        template_image_url=template_image_url,
    )
    featured_image.save()

    # Clear current featured image
    article.selected_featured_image = None
    article.featured_images.clear()

    article.selected_featured_image = featured_image
    article.featured_images.add(featured_image)
    article.save()

    logger.info(f"Featured image for article {article.article_uid} has been saved")


def post_featured_image_to_wp(
        article: Article,
        wp_site_url: str | None,
        update_media_id: int | None = None
) -> int:
    """
    Posts featured image of given article to Wordpress site. Returns wordpress media id (integer).

    :param article: Article model object.
    :param wp_site_url: Wordpress site URL
    """
    if not article.selected_featured_image:
        raise Exception(f"Error in post_featured_image_to_wp - "
                        f"No featured image available to post for article {article.article_uid}.")

    user: User = article.user

    filename: str = f"{user.username.lower().replace('.', '')}-{secrets.token_hex(32)}.{user.images_file_format}"
    headers = {
        'Content-Disposition': f"attachment; filename={filename}",
        'Content-Type': f"image/{user.images_file_format}",
        'User-Agent': random.choice(USER_AGENTS)
    }

    wordpress_integration: WordpressIntegration = article.website.wordpressintegration_set.filter(site_url=wp_site_url).first()

    if not wordpress_integration:
        wordpress_integration: WordpressIntegration = article.website.wordpressintegration_set.first()

    # Fetch the wordpress routes
    routes = get_wordpress_routes(wordpress_integration.site_url)

    if not routes:
        # Failed to fetch the wordpress routes
        raise Exception(f"Failed to fetch the wordpress rest routes for {wordpress_integration.site_url}")

    # Construct the URL
    url = routes['media']

    # Define http method
    http_method = "POST"

    # Try fetching image from url
    image_response = requests.get(article.selected_featured_image.image_url)
    if 300 > image_response.status_code >= 200:
        res = requests.request(
            method=http_method,
            url=url,
            data=image_response.content,
            headers=headers,
            auth=(wordpress_integration.user_login, wordpress_integration.password),
            timeout=120
        )
        if 300 > res.status_code >= 200:
            wp_response_data: Dict = res.json()
            featured_image_id: int = wp_response_data['id']

            if update_media_id:
                logger.info(f"Successfully updated media with ID {update_media_id} for article {article.article_uid}.")
            else:
                logger.info(f"Successfully created media with ID {featured_image_id} for article {article.article_uid}.")

            return featured_image_id

        else:
            # Fetaured image will not be added for this post
            logger.error(res.text)
            raise Exception(f"Error in post_featured_image_to_wp - "
                            f"Failed to upload featured image to Wordpress for article {article.article_uid}. "
                            f"Status Code {res.status_code}")

    else:
        # Fetaured image will not be added for this post
        raise Exception(f"Error in post_featured_image_to_wp - "
                        f"Failed to fetch featured image content for image url for article {article.article_uid}. "
                        f"Status Code {image_response.status_code}")


# def generate_bannerbear_featured_image__async(template_data: Dict):
#     """
#     Sends request to Bannerbear to generate image for blog post featured image using provided template data.
#     Results will be received via webhook endpoint defined in env file.

#     :param template_data: Bannerbear template data defined in bannerbear_templates.py.
#     """

#     # add webhook data to template for async
#     template_data.update({"webhook_url": os.environ['BANNERBEAR_WEBHOOK']})

#     res = requests.post(
#         url="https://api.bannerbear.com/v2/images",
#         json=template_data,
#         headers={
#             "Content-Type": "application/json",
#             "Authorization": f"Bearer {os.environ['BANNERBEAR_API_KEY']}"
#         }
#     )

#     if res.status_code != 202:
#         raise Exception(f"Bannerbear request failed with status code {res.status_code}")


# def generate_bannerbear_featured_image__sync(template_data: Dict, retries=3) -> Optional[Dict]:
#     """
#     Sends request to Bannerbear sync endpoint to generate image for blog post featured image immediately.

#     :param template_data: Bannerbear template data defined in bannerbear_templates.py.
#     :param retries: Number of retries to perform.
#     """
#     if retries <= 0:
#         logger.error("Could not generate bannerbear featured image: All retries exhausted.")
#         return None

#     res = requests.post(
#         url="https://sync.api.bannerbear.com/v2/images",
#         json=template_data,
#         headers={
#             "Content-Type": "application/json",
#             "Authorization": f"Bearer {os.environ['BANNERBEAR_API_KEY']}"
#         }
#     )
#     if 200 <= res.status_code < 300:
#         logger.debug(res.json())
#         return {
#             'image_url_png': res.json()['image_url_png'],
#             'article_uid': res.json()['metadata'],
#         }
#     elif res.status_code == 408:
#         return generate_bannerbear_featured_image__sync(template_data, retries - 1)

#     else:
#         logger.error(f"Could not generate bannerbear featured image: Unhandled status code {res.status_code}.")
#         return None


# def generate_bannerbear_performance_chart__sync(logo_url: str, website_domain: str, retries=3) -> Optional[Dict]:
#     """
#     Sends request to Bannerbear sync endpoint to generate image for content plan performance chart

#     :param logo_url:
#     :param website_domain:
#     :param retries: Number of retries to perform.
#     """
#     if retries <= 0:
#         logger.error("Could not generate bannerbear featured image: All retries exhausted.")
#         return None

#     res = requests.post(
#         url="https://sync.api.bannerbear.com/v2/images",
#         json=template_performance_chart(logo_url, website_domain),
#         headers={
#             "Content-Type": "application/json",
#             "Authorization": f"Bearer {os.environ['BANNERBEAR_API_KEY']}"
#         }
#     )
#     if 200 <= res.status_code < 300:
#         logger.debug(res.json())
#         return {
#             'image_url_png': res.json()['image_url_png'],
#             'website_domain': res.json()['metadata'],
#         }
#     elif res.status_code == 408:
#         return generate_bannerbear_performance_chart__sync(logo_url, website_domain, retries - 1)

#     else:
#         logger.error(f"Could not generate bannerbear featured image: Unhandled status code {res.status_code}.")
#         return None


def send_email(to: str | List,
               sender: str,
               sender_name: str,
               subject: str,
               body_html: str,
               files: Optional[List[InMemoryUploadedFile]] = None,
               reply_to: Optional[str] = "<EMAIL>"):
    """
    Sends email through AWS SES. Supports both single and multiple recipients.

    :param to: Recipient email address. Can be a string or list of strings.
    :param sender: Sender (our) email address.
    :param sender_name: Sender name.
    :param subject: Subject of email.
    :param body_html: Body (HTML/plaintext) content of email.
    :param reply_to: Reply-To email address.
    :param files:
    :return:
    """
    CHARSET = "UTF-8"

    msg = MIMEMultipart()
    msg['Subject'] = subject
    msg['From'] = f"{sender_name} <{sender}>"
    msg['Reply-To'] = reply_to

    if isinstance(to, str):
        msg['To'] = to
    else:
        msg['To'] = ', '.join(to)

    htmlpart = MIMEText(body_html, 'html', CHARSET)
    msg.attach(htmlpart)

    if files:
        for file in files:
            attachment = MIMEApplication(file.open().read())
            attachment.add_header('Content-Disposition', 'attachment', filename=file.name)
            msg.attach(attachment)

    client = boto3.client('ses', region_name='us-east-1')
    client.send_raw_email(
        Source=f"{sender_name} <{sender}>",
        Destinations=[to] if isinstance(to, str) else to,
        RawMessage={
            'Data': msg.as_string(),
        },
    )


def get_keyword_volume_data(keywords: List[str], country: str ="global" ) -> List[Dict]:
    """
    Returns volume data for given keywords.
    """
    payload = {
        'dataSource': "gkp",
        'country': country,
        'currency': "USD",
        'kw': keywords
    }

    # remove country if global
    # if country == "global":
    #     payload.pop('country')

    headers = {
        'Authorization': f'Bearer {os.environ["KEYWORDSEVERYWHERE_API_KEY"]}',
        'Accept': 'application/json',
    }

    try:
        res = requests.post(
            url="https://api.keywordseverywhere.com/v1/get_keyword_data",
            json=payload,
            headers=headers
        )
    except Exception as err:
        logger.error(err)
        return []

    if res.status_code == 200:
        keyword_data = res.json()['data']
        # If all keywords have 0 volume, then get global volume
        zero_vol_kwds_count = sum(not keyword_data["vol"] for keyword_data in keyword_data)

        if zero_vol_kwds_count == len(keyword_data) and country != "global":
            keyword_data = get_keyword_volume_data(keywords, 'global')

        return keyword_data

    else:
        logger.error(res.content)
        logger.error(f"Keywordseverywhere volume (paid) API returned bad status code: {res.status_code}")

    return []

def clean_keywords(keywords):
    '''
    Cleans a list of keyword strings by unescaping HTML entities, 
    removing non-alphanumeric characters (except whitespace), trimming spaces, 
    and filtering out empty or numeric-only keywords.
    '''
    cleaned = []
    for kw in keywords:
        kw = html.unescape(kw)
        kw = re.sub(r"[^\w\s]", '', kw, flags=re.UNICODE).strip()
        if kw and not kw.isdigit():  # Exclude numeric-only
            cleaned.append(kw)

    return cleaned

def get_keyword_volume_data_v2(keywords: List[str], location_code: int = 0) -> List[Dict]:
    """
    Returns volume data for given keywords from dataforseo API.
    """
    try:
        credentials = f"{os.environ['DATAFORSEO_LOGIN']}:{os.environ['DATAFORSEO_PASSWORD']}"

        # Encode the concatenated string using base64
        encoded_credentials = base64.b64encode(credentials.encode('utf-8')).decode('utf-8')

        url = "https://api.dataforseo.com/v3/keywords_data/google_ads/search_volume/live"

        # Unescape HTML and sanitize to keep only alphanumeric characters
        # keywords = [re.sub(r'[^a-zA-Z0-9\s]', '', html.unescape(kw)).strip() for kw in keywords]
        keywords = clean_keywords(keywords)
        logger.debug(f"Keywords: {len(keywords)}")

        payload = json.dumps([{"keywords": keywords, "language_code": "en"}])

        if location_code and location_code != 1:
            payload = json.dumps([{"keywords": keywords, "language_code": "en", "location_code": location_code}])

        headers = {
            'Authorization': "Basic " + encoded_credentials,
            'Content-Type': 'application/json'
        }

        try:
            response = requests.request("POST", url, headers=headers, data=payload)

            if response.status_code == 200:
                json_response = response.json()
                if not json_response['tasks'][0]['result'] and json_response['tasks'][0]['result_count'] == 0:
                    raise Exception(f"No results found in response: {json_response['tasks'][0]['status_message']}")
                results = json_response['tasks'][0]['result']

                # If all keywords have 0 volume, then get global volume
                zero_vol_kwds_count = sum(data.get("search_volume", 0) == 0 for data in results)
                if zero_vol_kwds_count == len(results) and location_code != 2840:
                    results = get_keyword_volume_data_v2(keywords, 2840)

                return results if results else []

            else:
                logger.error(f"Dataforseo volume (paid) API returned bad status code: {response.status_code}")
                raise Exception(f"dataforseo volume (paid) API returned bad status code: {response.status_code}")
        except Exception as e:
            logger.critical(f"Error in get_keyword_volume_data_v2: {e}")
            return []
    except Exception as e:
        logger.critical(f"Error in get_keyword_volume_data_v2: {e}")
        return []


def get_list_of_suggested_keywords(primary_keyword: str, location_data: Dict) -> List[str]:
    """
    Returns 125 keywords from top suggestions for given keyword.
    Fetchs keywords volume data in bulk for ~105 keywords collected from top 4 suggestions per alphabet from a to z after the user entered keyword and then will finally filter out keywords with 0 volume and save the rest keywords in the database.

    """
    country_code = location_data.get(
        "country", "us"
    ).lower()  # Default to 'us' if country is not found
    language_code = location_data.get(
        "country", "en"
    ).lower()  # Default to 'en' if language is not found

    url = "http://suggestqueries.google.com/complete/search"
    params = {
        "client": "firefox",
        "q": primary_keyword,
        "gl": country_code,
        "hl": language_code,
    }

    All_keywords_suggestions = [primary_keyword]
    # Get the suggestions
    try:
        for alphabet in string.ascii_lowercase:
            params["q"] = primary_keyword + " " + alphabet
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            suggestions = json.loads(response.text)
            # add upto 75 suggestions
            All_keywords_suggestions.extend(suggestions[1][:75])
    except requests.exceptions.Timeout:
        logger.error("Request timed out")
        raise Exception("Request timed out")
    except requests.exceptions.RequestException as e:
        logger.error(e)
        raise Exception("Request exception")

    return All_keywords_suggestions


def get_keywords_from_domain(domain: str) -> Dict:
    """
    Fetches keywords data from keywordseverywhere api.

    :param domain: ex. draftss.com
    """
    payload = {
        'domain': domain,
        'version': '11.30',
        'country': 'global',
        'num': 2000,
        'api_key': os.environ["KEYWORDSEVERYWHERE_API_KEY"],
    }
    headers = {
        'Accept': 'application/x.seometrics.v3+json',
        'content-type': 'application/json'
    }

    res = requests.post(
        url="https://data.keywordseverywhere.com/service/get-domain-keywords",
        json=payload,
        headers=headers
    )

    if res.status_code == 200:
        results = res.json()
        if (not results) or (not results['data']):
            # try again with www
            payload['domain'] = "www." + domain
            res = requests.post(
                url="https://data.keywordseverywhere.com/service/get-domain-keywords",
                json=payload,
                headers=headers
            )
            if res.status_code == 200:
                results = res.json()
                if (not results) or (not results['data']):
                    raise Exception(f"No keywords found for {domain}")
                else:
                    return res.json()
            else:
                logger.error(res.content)
                raise Exception(f"Keywordseverywhere domain keyword API returned bad status code: {res.status_code}")
        else:
            return res.json()
    else:
        logger.error(res.content)
        raise Exception(f"Keywordseverywhere domain keyword API returned bad status code: {res.status_code}")


def get_keyword_count(domain: str) -> int:
    """
    Returns keyword count from KeywordsEverywhere API.

    :param domain: website domain ex. draftss.com
    :returns: keyword count
    """
    try:
        keywords_data: Dict = get_keywords_from_domain(domain)
        total_keywords: int = keywords_data['total_keywords']
        logger.debug(f"{total_keywords} keywords for {domain}")
        if total_keywords == 0:
            logger.debug(f"got 0 total_keywords for {domain}")
            raise KeywordsEverywhereNoData(domain)

        return total_keywords

    except KeyError:
        logger.debug(f"No keyword data for {domain}")
        raise KeywordsEverywhereNoData(domain)

    except TypeError:
        logger.debug(f"No keyword data for {domain}")
        raise KeywordsEverywhereNoData(domain)

    except requests.exceptions.JSONDecodeError:
        logger.debug(f"No valid JSON response for {domain}")
        raise KeywordsEverywhereJSONError(domain)


def send_verification_email(user: User):
    """
    Sends account verification email to user.

    :param user: User model object
    """
    f = Fernet(EMAIL_VERIFICATION_ENCRYPTION_KEY)
    encrypted_email: bytes = f.encrypt(user.email.encode('utf-8'))
    # using RESET_PASSWORD_LINK_DOMAIN here is fine since the domain will be same anyways.
    verification_link: str = f"{RESET_PASSWORD_LINK_DOMAIN}/verify-account-email/{encrypted_email.decode('utf-8')}"
    email_message: str = account_email_verification_email_body(user.username, verification_link)
    send_email(user.email,
               ABUN_NOTIFICATION_EMAIL,
               "Team Abun",
               "Verify your account email address",
               email_message)


def get_valid_competitors(competitor_domains: List[str]):
    """
    For given list of competitor domains, this function returns a list of data for competitors which are valid and
    have 100 or more keywords available.

    :param competitor_domains: List of comeptitor domains (ex. draftss.com)
    """

    def _fetch_and_validate(domain: str):
        tld = tldextract.extract(domain)
        if (tld.domain and tld.suffix) and (tld.domain not in seen):
            seen.append(tld.domain)
            cleaned_domain = tld.domain + "." + tld.suffix
            # Get clearbit data for this domain
            res = requests.get(f"https://autocomplete.clearbit.com/v1/companies/suggest?query={cleaned_domain}")
            if res.status_code == 200:
                clearbit_json: List[Dict] = res.json()
                if len(clearbit_json) > 0:
                    # Get keyword count using KeywordsEverywhere API
                    try:
                        keyword_count: int = get_keyword_count(cleaned_domain)
                    except KeywordsEverywhereNoData:
                        # Try again
                        try:
                            keyword_count: int = get_keyword_count(f"www.{cleaned_domain}")
                        except KeywordsEverywhereNoData:
                            keyword_count = 0
                        except KeywordsEverywhereJSONError:
                            keyword_count = 0
                    except KeywordsEverywhereJSONError:
                        keyword_count = 0

                    # Only select those competitors which have 100 or more keywords available
                    # if keyword_count >= 100:

                    clearbit_domain_data: Dict = clearbit_json[0]
                    return {
                        'name': clearbit_domain_data['name'],
                        'domain': cleaned_domain,
                        'protocol': "https",
                        'keyword_count': keyword_count
                    }
            else:
                logger.error(f"get_valid_competitors() - Clearbit returned non-success status code: {res.status_code}")

    # remove duplicate domains
    competitor_domains = list(set(competitor_domains))

    seen: List[str] = []  # For holding already seen competitor domains (ex. draftss, designpickle)
    valid_comp_data: List[Dict] = []  # [{name, domain, protocol, keyword_count}, ...]
    with ThreadPoolExecutor(max_workers=25) as executor:
        futures = [executor.submit(_fetch_and_validate, domain) for domain in competitor_domains]
        for future in concurrent.futures.as_completed(futures):
            result: Dict = future.result()
            if result:
                valid_comp_data.append(result)

    return valid_comp_data


class ICP(BaseModel):
    industry: str = Field(
        description="Please provide the industry category for the website in less than 5 words (e.g., technology, healthcare, finance)"
    )
    icp: str = Field(
        description="Describe the target audience for this website in a concise sentence (less than 25 words)."
    )


@retry(langchain.schema.OutputParserException, tries=3, delay=2)
def get_industry_icp_llm_response(domain: str, title: str, description: str) -> ICP:
    """
    Runs gpt and returns results for industry and icp.
    :param domain: Website domain (ex. draftss.com)
    :param title: Website title.
    :param description: Website description.
    """
    llm = ChatOpenAI(model_name=get_gpt_model_name(), temperature=0.3)
    parser = PydanticOutputParser(pydantic_object=ICP)
    prompt = PromptTemplate(
        template=get_industry_icp_prompt_text(),
        input_variables=["title", "description", "domain"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )

    _input = prompt.format_prompt(title=title, description=description, domain=domain)
    _response = llm.invoke(_input.to_string())
    _output = _response.content

    total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
    add_or_update_gpt_token_usage(total_tokens)

    structured_output = parser.parse(_output)
    return structured_output


def chunker(seq, size):
    return (seq[pos:pos + size] for pos in range(0, len(seq), size))


def add_ai_tool_result_in_redis(tool_name, result):
    """
    Add AI tool result in redis DB.
    :param tool_name: AI tool name
    :param result: AI tool result
    """
    with get_redis_connection(db=REDIS_MINI_AI_TOOL_DB) as redis_connection:
        unique_hash = hashlib.sha256(f"{time.time()}-{random.random()}".encode('utf-8')).hexdigest()[:16]
        content_plan_redis_key: str = f'{tool_name}-{unique_hash}'
        tool_usage_counter = f'{tool_name}-counter'
        redis_connection.set(content_plan_redis_key, result)
        redis_connection.expire(content_plan_redis_key, REDIS_MINI_AI_TOOL_EXPIRY)

        try:
            counter = redis_connection.get(tool_usage_counter)
            if counter:
                redis_connection.incrby(tool_usage_counter, 1)
                # logger.debug('tool_usage_counter--',counter)
            else:
                redis_connection.set(tool_usage_counter, 0)
        except Exception as err:
            logger.critical(err)
            redis_connection.set(tool_usage_counter, 0)


def validate_input(input_string, input_name='input', min_length=1, max_length=100):
    """
    Function to validate Mini AI tools Input.
    param input_string: Input string
    param input_name: Input name
    param min_length: Minimum length
    param max_length: Maximum length
    returns: True/False, error message
    """
    if len(input_string) == 0:
        return False, f"{input_name} Required."

    if not input_string.strip():
        return False, f"{input_name} should not contain only space or special characters."

    if not (min_length <= len(input_string) <= max_length):
        return False, f"{input_name} length should be between {min_length} and {max_length} characters."

    if input_string.isdigit():
        return False, f"{input_name} should not contain only numeric values."

    if not re.search(r'[a-zA-Z0-9]', input_string):
        return False, f"{input_name} should contain at least one alphanumeric character."

    return True, "Valid Input"


# def revoke_webflow_authorization(user: User, collection_id: str, retries: int = 3, delay: int = 10) -> bool:
#     """
#     Revokes the webflow authorization
#     :param user: User model object
#     :param collection_id: Webfow collection ID
#     :param retries: Number of retries to make if we get 429 status code (Too Many Requests)
#     :param delay: Number of seconds to pause the execution
#     """
#     if retries <= 0:
#         logger.critical("All retries failed to revoke webflow authorization")
#         return False

#     webflow_integrations: QuerySet[WebflowIntegration] = user.webflow_integrations.filter(collection_id=collection_id)

#     if not webflow_integrations:
#         return True

#     for webflow_integration in webflow_integrations:
#         revoke_endpoint = 'https://api.webflow.com/oauth/revoke_authorization'
#         access_token = webflow_integration.token
#         data = {
#             'client_id': WEBFLOW_CLIENT_ID,
#             'client_secret': WEBFLOW_CLIENT_SECRET,
#             'access_token': access_token
#         }

#         res = requests.post(revoke_endpoint, data=data)

#         if res.status_code == 429:
#             time.sleep(delay)
#             delay += 10
#             retries -= 1
#             return revoke_webflow_authorization(user, collection_id, retries, delay)

#         logger.debug(res.json())

#     return True


def get_word_count(data: Any):
    pattern = r"[a-zA-Z0-9_\u00A0-\u02AF\u0392-\u03c9\u0410-\u04F9]+|[\u4E00-\u9FFF\u3400-\u4dbf\uf900-\ufaff\u3040-\u309f\uac00-\ud7af]+"
    matches = re.findall(pattern, data)
    count = 0
    if matches is None:
        return count
    for match in matches:
        if ord(match[0]) >= 0x4E00:
            count += len(match)
        else:
            count += 1
    return count


def add_website_log(user: User, domain: str, message: str | List,
                    connection_type: None | Literal['connected', 'disconnected'] = None):
    """
    Used to create a log on WebsiteLogs models
    :param user: User model instance
    :param domain: Website domain name
    :param message: Message to log (string or list)
    :param connection_type: Connection type defaults to None otherwise should be 'connected' or 'disconnected'
    """
    if not type(message) is list:
        website_log = WebsiteLogs(
            user=user,
            domain=domain,
            message=message,
            connection_type=connection_type
        )
        website_log.save()

        return None

    bulk_create_logs = []
    for msg in message:
        bulk_create_logs.append(
            WebsiteLogs(
                user=user,
                domain=domain,
                message=msg,
                connection_type=connection_type
            )
        )

    WebsiteLogs.objects.bulk_create(bulk_create_logs)


def download_and_save_image(image_url: str, article_uid: str) -> str:
    """
    Used to save the article image
    :param image_url: Image url
    :param article_uid: Article Uid
    """
    try:
        response = requests.get(image_url, timeout=30)

        if response.ok:
            article_image = ArticleImage()

            try:
                user = Article.objects.get(article_uid=article_uid).user
                images_file_format = user.images_file_format

            except Article.DoesNotExist as e:
                logger.error(f"Error while fetching user images_file_format: {e}")
                images_file_format = 'png'

            total_images = ArticleImage.objects.filter(
                image__startswith=f"{os.environ['CLOUDFLARE_R2_ENV']}/article-images/{article_uid}"
            ).count()
            image_name = f"{article_uid}-{total_images+1}"

            if images_file_format == 'png':
                image_name += ".png"
                article_image.image.save(image_name, ContentFile(response.content))
            else:
                # Convert image to webp or jpeg format as per website settings
                try:
                    img = Image.open(BytesIO(response.content))
                    img = img.convert("RGB")
                    buffer = BytesIO()

                    if images_file_format == 'webp':
                        img.save(buffer, format="webp")
                        image_name += ".webp"

                    elif images_file_format == 'jpeg':
                        img.save(buffer, format="jpeg")
                        image_name += ".jpg"

                    article_image.image.save(image_name, ContentFile(buffer.getvalue()))

                except Exception as e:
                    logger.error(f"Error while converting image to {images_file_format}: {e}")
                    image_name += ".png"
                    article_image.image.save(image_name, ContentFile(response.content))

            return article_image.image.url

        else:
            logger.critical(f"Failed to save the article image. Status code {response.status_code}")

    except Exception as err:
        logger.critical(err)

    return ""


def download_and_save_image_bytes(image_content: str | bytes, article_uid: str) -> str:
    """
    Used to save the article image
    :param image_url: Image url
    :param article_uid: Article Uid
    """
    try:
        article_image = ArticleImage()

        try:
            user = Article.objects.get(article_uid=article_uid).user
            images_file_format = user.images_file_format

        except Article.DoesNotExist as e:
            logger.error(f"Error while fetching user images_file_format: {e}")
            images_file_format = 'png'

        total_images = ArticleImage.objects.filter(
            image__startswith=f"{os.environ['CLOUDFLARE_R2_ENV']}/article-images/{article_uid}"
        ).count()
        image_name = f"{article_uid}-{total_images+1}"

        if images_file_format == 'png':
            image_name += ".png"
            article_image.image.save(image_name, ContentFile(image_content))
        else:
            # Convert image to webp or jpeg format as per website settings
            try:
                img = Image.open(BytesIO(image_content))
                img = img.convert("RGB")
                buffer = BytesIO()

                if images_file_format == 'webp':
                    img.save(buffer, format="webp")
                    image_name += ".webp"

                elif images_file_format == 'jpeg':
                    img.save(buffer, format="jpeg")
                    image_name += ".jpg"

                article_image.image.save(image_name, ContentFile(buffer.getvalue()))

            except Exception as e:
                logger.error(f"Error while converting image to {images_file_format}: {e}")
                image_name += ".png"
                article_image.image.save(image_name, ContentFile(image_content))

        return article_image.image.url

    except Exception as err:
        logger.critical(err)

    return ""


# ----------------------------------------------------------------------------------------------------------------------
# ------------------------------------------ AI Featured Image Generation ----------------------------------------------
# ----------------------------------------------------------------------------------------------------------------------
def segmind_ai_img_gen_api_call(flux_dev_prompt: str, article_uid: str, type: str = "featured_image") -> Optional[Dict]:
    """
    Fetches AI generated image from Segmind API.
    """
    try:
        headers = {
            "x-api-key": os.environ["SEGMIND_API_KEY"],
        }
        data = {
            "prompt": str(flux_dev_prompt),
            "steps": 20,
            "seed": random.randint(1, 999999999), # Random 9 digit number
            "sampler_name": "euler",
            "scheduler": "normal",
            "samples": 1,
            "width": type == "featured_image" and 1280 or 1024,
            "height": type == "featured_image" and 720 or 512,
            "aspect_ratio": type == "featured_image" and "16:9" or "16:9",
            "denoise": 1,
            "guidance": 3.5,
            "steps": 25,
            "prompt_strength": 0.8,
            "output_quality": 100,
        }
        response = requests.post(
            url="https://api.segmind.com/v1/flux-dev",
            headers=headers,
            json=data
        )
        if response.status_code == 200:
            try:
                # response will be in bytes format
                image_url = download_and_save_image_bytes(response.content, article_uid)
                if image_url:
                    return {
                        "image_url": image_url
                    }
                else:
                    return {
                        "image_url": "https://cdn.abun.com/abun-media%2Fdefault-featured-image.jpeg"
                    }
            except Exception as e:
                raise Exception(f"Failed to parse AI image response - {e}")
        else:
            raise Exception(f"Non-success status code received from Segmind API {response.json()}")
    except Exception as e:
        logger.error(f"AI image generation failed - {e}")
        return {
            "image_url": "https://cdn.abun.com/abun-media%2Fdefault-featured-image.jpeg"
        }


def deepinfra_ai_img_gen_api_call(prompt: str, article_uid: str, generation_type: str = "featured_image") -> Optional[Dict]:
    """
    Sends request to DeepInfra API to generate image for blog post featured image immediately.

    :param sdxl_prompt: SDXL prompt string.
    :param type: Type of image to generate. Defaults to "featured_image".
    :param article_uid: Article UID
    """
    try :
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"bearer {os.environ['DEEPINFRA_API_TOKEN']}",
        }
        data = {
            "prompt": prompt,
            "num_outputs": 1,
            "num_inference_steps": 10,
            "width": generation_type == "featured_image" and 1280 or 1024,
            "height": generation_type == "featured_image" and 720 or 512,
        }
        response = requests.post(
            url="https://api.deepinfra.com/v1/inference/black-forest-labs/FLUX-1-schnell",
            headers=headers,
            json=data,
        )

        if response.status_code == 200:
            try:
                response_json = response.json()
                if response_json["images"]:
                    width = generation_type == "featured_image" and 1280 or 1024
                    height = generation_type == "featured_image" and 720 or 512
                    calculated_cost = 0.0005 * (width/1024) * (height/1024) * 10
                    logger.info(f"Image generation cost for {article_uid}: {calculated_cost} dollars")
                    base64_data: str = response_json["images"][0].split(',')[1]
                    image_data: bytes = base64.b64decode(base64_data)
                    image_url = download_and_save_image_bytes(image_data, article_uid)
                    if image_url:
                        return {
                            "image_url": image_url
                        }
                    else:
                        return {
                            "image_url": response_json["images"][0]
                        }
                else:
                    raise Exception(f"response_json is None")
            except Exception as e:
                raise Exception(f"Failed to parse AI image response - {e}")

        else:
            raise Exception(f"Non-success status code received from deepinfra API {response.json()}")

    except Exception as e:
        logger.error(f"AI image generation failed - {e}")
        return {
            "image_url": "https://cdn.abun.com/abun-media%2Fdefault-featured-image.jpeg"
        }


def generate_AI_feature_image__sync(article_uid: str,
                                    provider_name: Literal["deepinfra", "segmind"]) -> Optional[Dict]:
    """
    Fetches AI generated image from deepinfra API.

    :param article_uid: Article UID for which the feature image is to be generated.
    """
    try:
        article = Article.objects.get(article_uid=article_uid)

        llm = ChatOpenAI(
            model_name=get_gpt_model_name(),
            temperature=0.7,
            max_tokens=4096
        )

        # Use DeepInfra's Flux-Schnell AI Image Generation
        if provider_name == 'deepinfra':
            logger.debug("Using DeepInfra's 'Flux-Schnell' featured image generation")

            # For templates without text, generate visual description instead of rephrasing title
            if "-without-text" in article.user.feature_image_template_id:
                article_input: str = generate_visual_description_from_title(article)
            else:
                # For templates with text, rephrase the title
                article_input: str = rephrase_article_title(article)

            prompt = PromptTemplate(
                template=segmind_ai_img_context_prompt_text(article.user.feature_image_template_id),
                input_variables=["blog_title"]
            )

            _input = prompt.format_prompt(blog_title=article_input)
            _response = llm.invoke(_input.to_string())

            total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            llm_prompt = _response.content
            return deepinfra_ai_img_gen_api_call(llm_prompt, article_uid)

        # Use Segmind AI Image Generation for generating the image
        else:
            logger.debug("Using Segmind's 'Flux-Dev' model to generate featured image.")

            # For templates without text, generate visual description instead of rephrasing title
            if "-without-text" in article.user.feature_image_template_id:
                article_input: str = generate_visual_description_from_title(article)
            else:
                # For templates with text, rephrase the title
                article_input: str = rephrase_article_title(article)

            prompt = PromptTemplate(
                template=segmind_ai_img_context_prompt_text(article.user.feature_image_template_id),
                input_variables=["blog_title"]
            )

            _input = prompt.format_prompt(blog_title=article_input)
            _response = llm.invoke(_input.to_string())

            total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            llm_prompt = _response.content
            return segmind_ai_img_gen_api_call(llm_prompt, article_uid)

    except Exception as e:
        logger.error(f"Featured img gen failed for {article_uid} - {e}")
        return {
            "image_url": "https://cdn.abun.com/abun-media%2Fdefault-featured-image.jpeg"
        }


@transaction.atomic
def add_webflow_integration(user: User, access_token: str, site_id: str, fields_mapping: List[Dict],
                            collection_id: str, slug: str, site_url: str, based_on: Literal['app', 'api'] = 'app'):
    """
    Saves Webflow integration details to user model. This will also delete any existing
    cms integration data for this user.

    :param user: User model object. Integration will be added to this user.
    :param access_token: Webflow access token to make authenticated requests to the API.
    :param site_id: Webflow site ID
    :param fields_mapping: Webflow CMS collectoin fields mapping
    :param collection_id: Webflow collection ID
    :param slug: Collection slug to create article posted link
    :param site_url: Webflow site url (ex. https://testing.abun.com/)
    :param based_on: Integration based on app or api
    """
    # Create the new integration
    webflow_integration = WebflowIntegration(
        website=user.current_active_website,
        token=access_token,
        site_id=site_id,
        collection_id=collection_id,
        collection_slug=slug,
        site_url=site_url,
        collection_fields=fields_mapping,
        based_on=based_on
    )
    webflow_integration.save()


def publish_article_to_webflow(article: Article,
                               webflow_integration: WebflowIntegration,
                               retries: int = 5,
                               delay: int = 10,
                               draft: bool = False,
                               update_published_article: bool = False) -> Tuple[Dict, str | None]:
    """
    Publish an article to webflow
    :param article: Article instance
    :param webflow_integration: WebflowIntegration model instance
    :param retries: Number of retries to make if we get 429 status code (Too Many Requests)
    :param delay: Number of seconds to pause the execution
    :param update_published_article: True/False to update the publish article
    """
    if retries <= 0:
        logger.critical("All retries failed to publish the article to webflow")
        return {
            "status": "error",
            "err_id": "ALL_RETRIES_FAILED",
            "error_message": "All retries failed to publish the article to webflow"
        }, None

    article_title: str = unescape_amp_char(article.title)
    article_content: str = article.content
    html_content: str = markdown.markdown(article_content)
    feature_image: str = article.selected_featured_image.image_url if article.selected_featured_image else ""
    article_slug: str =  re.sub(r'[^a-zA-Z0-9 ]', '', article_title).replace(' ', '-').lower()
    article_description: str = article.article_description

    url = f"https://api.webflow.com/v2/collections/{webflow_integration.collection_id}/items"
    collection_fields = webflow_integration.collection_fields
    headers={'Authorization': f'Bearer {webflow_integration.token}', 'Content-Type': 'application/json'}
    payload = {
        "isArchived": False,
        "isDraft": draft,
        "fieldData": {}
    }

    fields = fetch_webflow_cms_collection_fields(webflow_integration.token, webflow_integration.collection_id)
    field_slug_and_index = {field["value"]: idx for idx, field in enumerate(collection_fields)}

    if fields is not None:
        all_slugs = field_slug_and_index.keys()

        for field in fields:
            if field['slug'] in all_slugs:
                collection_fields[field_slug_and_index[field['slug']]]['validations'] = field['validations']

    for field in collection_fields:
        if field['code'] == 'article_content':
            if field['validations']:
                max_length = field['validations'].get('maxLength', len(html_content))
                payload['fieldData'][field['value']] = html_content[:max_length]
            else:
                payload['fieldData'][field['value']] = html_content

        elif field['code'] == 'featue_image':
            payload['fieldData'][field['value']] = feature_image

        elif field['code'] == 'article_short_summary':
            short_summary = article_description

            if not short_summary:
                sentences = re.split(r'(?<=[.!?]) +', article_content)
                short_summary = ' '.join(sentences[:3])

            if field['validations']:
                short_summary_length = len(short_summary)
                max_length = field['validations'].get('maxLength', short_summary_length)
                payload['fieldData'][field['value']] = f"{short_summary[:(max_length - 3)]}..." \
                                                            if max_length < short_summary_length else short_summary
            else:
                payload['fieldData'][field['value']] = short_summary

        elif field['code'] == 'article_posted_date':
            payload['fieldData'][field['value']] = datetime.datetime.now(tz=ZoneInfo('UTC')).strftime('%Y-%m-%d %H:%M:%S')

        elif field['code'] == 'article_slug':
            payload['fieldData'][field['value']] = article_slug

        elif field['code'] == 'article_title':
            payload['fieldData'][field['value']] = article_title


    try:
        # payload['fieldData']['slug'] = article.url_slug or sanitize_url_slug(article.title.lower().replace(" ", "-")[:50])

        item_id = None
        if update_published_article:
            item_id = fetch_webflow_cms_collection_item(article.article_link, webflow_integration.collection_id, headers)

            if not item_id:
                logger.error(f"Article ID not found for update. Article link: {article.article_link}")
                return {
                    "status": "error",
                    "err_id": "ARTICLE_ID_NOT_FOUND",
                    "error_message": "Article ID not found for update"
                }, None

            url = f"https://api.webflow.com/v2/collections/{webflow_integration.collection_id}/items/{item_id}"
            http_method = "PATCH"

        else:
            url = f"https://api.webflow.com/v2/collections/{webflow_integration.collection_id}/items"
            http_method = "POST"

        res = requests.request(
            method=http_method,
            url=url,
            headers=headers,
            json=payload
        )

        if res.status_code == 429:
            time.sleep(delay)
            delay += 10
            retries -= 1
            return publish_article_to_webflow(article, webflow_integration, retries, delay)

        if not item_id:
            # Get the posted article ID
            posted_article_data = res.json()
            item_id = posted_article_data.get('id', None)

        if item_id:
            url = f"https://api.webflow.com/v2/collections/{webflow_integration.collection_id}/items/{item_id}"

            # Fetch the article data to get the slug
            posted_article_res = requests.get(url, headers=headers)

            # Get the slug
            webflow_item_response_data = posted_article_res.json()
            article_slug = webflow_item_response_data['fieldData']['slug']

        return {
            "status": "success" if res.ok else "error",
            "err_id": "FAILED_TO_POST_ARTICLE" if not res.ok else None,
            "post_status": webflow_item_response_data["isDraft"] and "draft" or "publish",
            "error_message": res.content if not res.ok else None,
        }, article_slug

    except Exception as err:
        logger.critical(f"Failed to post article: {err}")

        return {
            "status": "error",
            "err_id": "FAILED_TO_POST_ARTICLE",
            "error_message": f"Failed to post article: {err}"
        }, None


def create_weflow_cms_collection_field(access_token: str, collection_id: str, field_name: str, field_type: str,
                                       help_text: str, retries: int = 5, delay: int = 10) -> Dict:
    """
    Create CMS collection field in webflow
    :param access_token: Webflow access token
    :param collection_id: CMS collection ID
    :param field_name: Name of the Field
    :param field_type: Field type
    :param help_text: Help text
    """
    if retries <= 0:
        logger.critical("Failed to create CMS collection, all retries failed")
        return {
            'status': 429,
            'data': {'message': "You are making too many requests! Please try again after some time.", 'success': False}
        }

    headers={
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    # Create the field
    create_collection_field_collection = f"https://api.webflow.com/v2/collections/{collection_id}/fields"
    payload = {
        "type": field_type,
        "isRequired": True,
        "displayName": field_name,
        "helpText": help_text
    }

    res = requests.post(create_collection_field_collection, json=payload, headers=headers)
    response_data = res.json()

    if res.status_code == 429:
        time.sleep(delay)
        delay += 10
        retries -= 1
        return create_weflow_cms_collection_field(access_token, collection_id, field_name, field_type,
                                                  help_text, retries, delay)

    if not res.ok and 'content' not in response_data['message']:
        logger.error(f"Unable the create required field for '{collection_id}'. Response data -> {response_data}")
        return {
            'status': 400,
            'data': {'message': f"Error while creating the required fields for '{collection_id}'", 'success': False}
        }


def fetch_webflow_cms_collection_fields(access_token: str, collection_id: str, retries: int = 3,
                                        delay: int = 10) -> List[Dict] | None:
    """
    Used to fetch the cms collection fields details
    :param access_token: Webflow access token
    :param collection_id: Collection ID
    :param retries: Number of retries to make if we get 429 status code (Too Many Requests)
    :param delay: Number of seconds to pause the execution
    """
    if retries <= 0:
        logger.critical("All retries failed to fetch webflow CMS collection fields")
        return None

    headers={
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    # Fetch collection details
    collection_details_endpoint: str = f"https://api.webflow.com/v2/collections/{collection_id}"
    res: requests.Response = requests.get(collection_details_endpoint, headers=headers)

    if res.status_code == 429:
        time.sleep(delay)
        retries -= 1
        delay += 10
        return fetch_webflow_cms_collection_fields(access_token, collection_id, retries, delay)

    response_data: Dict = res.json()
    fields: List[Dict] = response_data['fields']
    return fields


def get_webflow_cms_collection_slug(access_token: str, collection_id: str,
                                    retries: int = 3, delay: int = 10) -> str | None:
    """
    Returns CMS collcetion slug used to create article posted link
    :param access_token: Webflow access token
    :param collection_id: Collection ID
    :param retries: Number of retries to make if we get 429 status code (Too Many Requests)
    :param delay: Number of seconds to pause the execution
    """
    if retries <= 0:
        logger.critical("All retries failed for get_webflow_cms_collection_slug()")
        return None

    headers={
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    # Fetch collection details
    collection_details_endpoint: str = f"https://api.webflow.com/v2/collections/{collection_id}"
    res: requests.Response = requests.get(collection_details_endpoint, headers=headers)

    if res.status_code == 429:
        time.sleep(delay)
        delay += 10
        retries -= 1
        return get_webflow_cms_collection_slug(access_token, collection_id, retries, delay)

    response_data: Dict = res.json()
    slug: str = response_data['slug']
    return slug


def fetch_webflow_cms_collections(access_token: str, site_id: str,
                                  retries: int = 5, delay: int = 10) -> List[Dict] | None:
    """
    Used to fetch the webflow cms collections
    :param access_token: Webflow access token
    :param site_id: Collection ID
    :param retries: Number of retries to make if we get 429 status code (Too Many Requests)
    :param delay: Number of seconds to pause the execution
    """
    if retries <= 0:
        logger.critical("All retries failed for fetch_webflow_cms_collections()")
        return None

    headers={
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    # Fetch all CMS collections
    list_collections_endpoint: str = f"https://api.webflow.com/v2/sites/{site_id}/collections"
    res: requests.Response = requests.get(list_collections_endpoint, headers=headers)

    if res.status_code == 429:
        time.sleep(delay)
        delay += 10
        retries -= 1
        return fetch_webflow_cms_collections(access_token, site_id, retries, delay)

    try:
        response_data: Dict = res.json()
    except json.JSONDecodeError:
        logger.error(f"Failed to decode JSON response: {res.text}")
        return None

    collections: List[Dict] = response_data['collections']
    return collections


def get_webflow_site_id_and_url(access_token: str, retries: int = 5, delay: int = 10) -> Dict:
    """
    Returns webflow site id and url
    :param access_token: Webflow access token
    :param retries: Number of retries to make if we get 429 status code (Too Many Requests)
    :param delay: Number of seconds to pause the execution
    """
    if retries <= 0:
        logger.critical("All retries failed for get_webflow_site_id_and_url()")
        return {
            'status': 429,
            'data': {'message': "You are making too many requests! Please try again after some time.", 'success': False}
        }

    headers={
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }

    # Fetch Site ID & URL
    list_sites_endpoint: str = 'https://api.webflow.com/v2/sites'
    res: requests.Response = requests.get(list_sites_endpoint, headers=headers)
    response_data: Dict = res.json()

    if res.status_code == 429:
        time.sleep(delay)
        delay += 10
        retries -= 1
        return get_webflow_site_id_and_url(access_token, retries, delay)

    if not res.ok:
        return {
            'status': 400,
            'data': {'message': response_data['message'], 'success': False}
        }

    try:
        site = response_data['sites'][0]
        site_id = site['id']

        if not site['customDomains']:
            site_url = f"https://{site['shortName']}.webflow.io"
        else:
            site_url = f"https://{site['customDomains'][0]['url']}"

    except KeyError:
        return {
            'status': 400,
            'data': {'message': "Missing required keys in webflow response.", 'success': False}
        }

    except IndexError:
        return {
            'status': 400,
            'data': {'message': "User does not have a domain or site to enable auto publish.", 'success': False}
        }

    return {
        'status': 200,
        'data': {
            'message': "OK",
            'success': True,
            'site_url': site_url,
            'site_id': site_id
        }
    }


# def setup_webflow_account_for_autopublish(access_token: str, retries: int = 5, delay: int = 10) -> Dict:
#     """
#     Used to setup webflow account for autopublish. Creates the CMS collection & requried fields
#     :param access_token: Webflow access token
#     :param retries: Number of retries to make if we get 429 status code (Too Many Requests)
#     :param delay: Number of seconds to pause the execution
#     """
#     if retries <= 0:
#         logger.critical("All retries failed for setup_webflow_account_for_autopublish()")
#         return {
#             'status': 429,
#             'data': {'message': "You are making too many requests! Please try again after some time.", 'success': False}
#         }

#     headers={
#         'Authorization': f'Bearer {access_token}',
#         'Content-Type': 'application/json'
#     }

#     # Fetch Site ID & URL
#     list_sites_endpoint: str = 'https://api.webflow.com/v2/sites'
#     res: requests.Response = requests.get(list_sites_endpoint, headers=headers)
#     response_data: Dict = res.json()

#     if res.status_code == 429:
#         time.sleep(delay)
#         delay += 10
#         retries -= 1
#         return setup_webflow_account_for_autopublish(access_token, retries, delay)

#     if not res.ok:
#         return {
#             'status': 400,
#             'data': {'message': response_data['message'], 'success': False}
#         }

#     # Get the site id & domain
#     try:
#         site = response_data['sites'][0]
#         site_id = site['id']

#         if not site['customDomains']:
#             site_url = f"https://{site['shortName']}.webflow.io"
#         else:
#             site_url = site['customDomains'][0]

#     except KeyError:
#         return {
#             'status': 400,
#             'data': {'message': "Missing required keys in webflow response.", 'success': False}
#         }

#     except IndexError:
#         return {
#             'status': 400,
#             'data': {'message': "User does not have a domain or site to enable auto publish.", 'success': False}
#         }

#     # Create collection ID and required fields if not already created
#     list_collections_endpoint: str = f"https://api.webflow.com/v2/sites/{site_id}/collections"
#     res: requests.Response = requests.get(list_collections_endpoint, headers=headers)

#     if res.status_code == 429:
#         time.sleep(delay)
#         delay += 10
#         retries -= 1
#         return setup_webflow_account_for_autopublish(access_token, retries, delay)

#     response_data: Dict = res.json()
#     collections: List[Dict] = response_data['collections']

#     # Stores boolean for collection & required field
#     create_collection = True
#     create_content_field = True
#     create_feature_image_field = True

#     # Check collection name if already exists
#     for collection in collections:
#         if collection['displayName'] == 'Abun Articles':
#             create_collection = False
#             collection_id = collection['id']
#             break

#     if not create_collection:
#         # Check fields name if already created
#         collection_details_endpoint: str = f"https://api.webflow.com/v2/collections/{collection_id}"
#         res: requests.Response = requests.get(collection_details_endpoint, headers=headers)

#         if res.status_code == 429:
#             time.sleep(delay)
#             delay += 10
#             retries -= 1
#             return setup_webflow_account_for_autopublish(access_token, retries, delay)

#         response_data: Dict = res.json()
#         fields: List[Dict] = response_data['fields']

#         for field in fields:
#             if field['displayName'] == 'content':
#                 create_content_field = False
#             elif field['displayName'] == 'image':
#                 create_feature_image_field = False

#             if not create_content_field and not create_feature_image_field:
#                 break

#     if create_collection:
#         # Create collection
#         create_collection_endpoint = f"https://api.webflow.com/v2/sites/{site_id}/collections"
#         payload = {
#             "displayName": "Abun Articles",
#             "singularName": "abun-articles"
#         }
#         res = requests.post(create_collection_endpoint, json=payload, headers=headers)

#         if res.status_code == 429:
#             time.sleep(delay)
#             delay += 10
#             retries -= 1
#             return setup_webflow_account_for_autopublish(access_token, retries, delay)

#         response_data = res.json()
#         collection_id = response_data['id']

#     if create_content_field:
#         # Create content field
#         res = create_weflow_cms_collection_field(access_token, collection_id, "content", "RichText", "Article content")

#         if res:
#             return res

#     if create_content_field:
#         # Create feature image field
#         res = create_weflow_cms_collection_field(access_token, collection_id, "image", "Image", "Article feature image")

#         if res:
#             return res

#     return {
#         'status': 200,
#         'data': {
#             'message': f"OK",
#             'success': True,
#             'access_token': access_token,
#             'site_id': site_id,
#             'collection_id': collection_id,
#             'site_url': site_url
#         }
#     }


def publish_webflow_site(site_id: str, access_token: str,
                         site_url: str, retries: int = 3, delay: int = 10) -> requests.Response | None:
    """
    Publish a site to webflow
    :param site_id: Webflow site id
    :param access_token: Webflow access token
    :param retries: Number of retries to make if we get 429 status code (Too Many Requests)
    :param delay: Number of seconds to pause the execution
    """
    if retries <= 0:
        logger.critical("All retries failed for publish_webflow_site()")
        return None

    url = f"https://api.webflow.com/v2/sites/{site_id}/publish"

    if "webflow.io" in site_url:
        payload = {
            'publishToWebflowSubdomain': True,
        }
    else:
        payload = {
            'publishToWebflowSubdomain': True,
            'customDomains': [site_url]
        }

    headers = {
        'accept': "application/json",
        'content-type': "application/json",
        'authorization': f"Bearer {access_token}"
    }

    response = requests.post(url, json=payload, headers=headers)

    if response.status_code == 429:
        time.sleep(delay)
        delay += 10
        retries -= 1
        return publish_webflow_site(site_id, access_token, site_url, retries, delay)

    logger.debug(response.json())
    return response


def create_article_generation_v2_task(user: User, article_uid: str, regenerate: bool = False) -> Dict:
    """
    Creates a new article generation task for the given article.

    :param article_uid: Article UID for which the article generation task is to be created.
    :param user: Related User model object.
    :param regenerate: If True, the article will be regenerated even if it has already been generated.
    """
    try:
        # ------------------- fetch article -------------------
        try:
            article: Article = Article.objects.get(article_uid=article_uid)
        except Article.DoesNotExist as e:
            raise ValueError(f"Article not found - {e}")

        # ------------------- Create K8 Job -------------------
        art_gen_job_id = generate_k8_job_id('articlegeneration', username=user.username)
        art_gen_data = {
            'article_uid': article_uid,
            'domain': user.current_active_website.domain if user.current_active_website else "no website connected",
            'title': article.title,
            'image_source': user.image_source,
            'abun_webhook_url': reverse('wh-k8-article-generation'),
            'article_tone_of_voice': user.article_tone_of_voice,
            'external_backlinks_preference': user.external_backlinks_preference,
            'article_language_preference': user.article_language_preference,
            'tone_of_article': user.tone_of_article,
            'scale_of_tone': user.scale_of_tone,
            'max_internal_backlinks': user.max_internal_backlinks,
            'max_external_backlinks': user.max_external_backlinks,
            'ai_generated_image_style': user.ai_generated_image_style,
            'other_top_ranking_urls': article.other_top_ranking_urls,
            'article_context': article.context,
            'article_youtube_urls': article.youtube_urls,
            'use_deepinfra_for_ai_img_gen': user.feature_image_template_label == "premium" and False or True,
            'toggle_table' : user.toggle_table if DEBUG else True,
            'toggle_meta_description': user.toggle_meta_description if DEBUG else True,
            'toggle_faq': user.toggle_faq if DEBUG else True,
            'toggle_tldr': user.toggle_tldr if DEBUG else True,
            'toggle_bullet_points': user.toggle_bullet_points if DEBUG else True,
        }

        if article is not None and article.keyword is not None:
            art_gen_data['keyword'] = article.keyword.keyword
        else:
            art_gen_data['keyword'] = None

        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            redis_connection.set(art_gen_job_id, json.dumps(art_gen_data))
            redis_connection.expire(art_gen_job_id, REDIS_ART_GEN_EXPIRY)

        art_gen_k8_job = KubernetesJob(
            job_id=art_gen_job_id,
            user=user,
            status='running',
            metadata=article_uid,
        )
        art_gen_k8_job.save()

        if os.environ['K8_IMAGE_TAG'] not in ['staging', 'production']:
            machine_id = "NOT_REQUIRED"
            job_created = create_k8_job(
                art_gen_job_id,
                'article_generation_v3',
                art_gen_job_id,
                user.id,
                [art_gen_job_id]
            )

            if not job_created:
                return {
                    "status": "error",
                    "err_id": "FAILED_TO_CREATE_ARTICLE_GENERATION_TASK",
                    "message": "Job is not created due to high load on the cluster."
                }

        else:
            # ------------------- Trigger Fly.io -------------------
            cmd = f"python3 article_gen.py {art_gen_job_id}"
            cmd = cmd.split()
            worker_props = {
                "config": {
                    "image": FLY_ARTICLE_GEN_IMAGE_URL,
                    "auto_destroy": True,
                    "init": {
                        "cmd": cmd
                    },
                    "restart": {
                        "policy": "on-failure",
                        "max_retries": K8_JOB_RETRIES
                    },
                    "guest": {
                        "cpu_kind": "shared",
                        "cpus": 1,
                        "memory_mb": 1024
                    }
                },
            }

            res = requests.post(
                f"{FLY_API_HOST}/apps/{FLY_ARTICLE_GEN_APP_NAME}/machines",
                headers={
                    'Authorization': f"Bearer {FLY_ARTICLE_GEN_DEPLOY_TOKEN}",
                    'Content-Type': 'application/json'
                },
                json=worker_props
            )

            if res.status_code != 200:
                logger.error(res.text)
                raise Exception("Failed to send the article generation task to fly.io")

            # Wait for the machine to enter the started state
            machine_id: str = res.json()['id']
            machine_name: str = res.json()['name']

            logger.debug(f"Machine ID: {machine_id}")
            logger.debug(f"Machine Name: {machine_name}")

        # ------------------- Update article status -------------------
        article.is_processing = True
        article.save()

        return {
            "status": "success",
            "job_id": art_gen_job_id,
            "machine_id": machine_id
        }

    except Exception as e:
        logger.error(f"Failed to create article generation task for {article_uid} - {e}")
        return {"status": "error", "err_id": "FAILED_TO_CREATE_ARTICLE_GENERATION_TASK", "message": str(e)}


def publish_article_to_wp(
        article: Article,
        user: User,
        status: str = "publish",
        wp_site_url: str | None = None,
        selected_categories: int | None = None,
        update_published_article: bool = False,
) -> Dict:
    """
    Used to publish an article to wordpress
    :param article: Article model instance
    :param user: User model instance
    :param status: Status of the article
    """
    try:
        if not update_published_article:
            wordpress_integration: WordpressIntegration = article.website.wordpressintegration_set.filter(site_url=wp_site_url).first()
        else:
            all_integrations: QuerySet[WordpressIntegration] = article.website.wordpressintegration_set.filter(site_url__isnull=False)

            wordpress_integration = None
            article_domain = urlparse(article.article_link).netloc.replace("www.", "")

            for integration in all_integrations:
                # Normalize and parse the integration site URL
                raw_site_url = integration.site_url
                if not raw_site_url.startswith("http"):
                    raw_site_url = "https://" + raw_site_url  # Assume https if scheme is missing

                integration_domain = urlparse(raw_site_url).netloc.replace("www.", "")

                if article_domain == integration_domain:
                    wordpress_integration = integration
                    break
           
        if not wordpress_integration:
            wordpress_integration: WordpressIntegration = article.website.wordpressintegration_set.first()

        # Fetch the wordpress credentials
        wp_site_url: str = wordpress_integration.site_url
        wp_admin_username: str = wordpress_integration.user_login
        wp_app_password: str = wordpress_integration.password

    except Exception as err:
            logger.critical(f"[*] Failed to connect with '{wp_site_url}' err--", err)
            return {"status": "error", "error_message": f"Failed to connect with {wp_site_url}"}

    if update_published_article:
        try:
            post_id, media_id = get_wp_postid_media_id(article, wp_site_url, wp_admin_username, wp_app_password)
            if not post_id:
                logger.error("Article ID not found for update")
                return {"status": "error", "err_id": "ARTICLE_ID_NOT_FOUND", "error_message": "Article ID not found for update"}

        except Exception as err:
            logger.error(f"Failed to retrieve post ID and media ID from WordPress: {err}")
            return {
                "status": "error",
                "error_message": f"Failed to retrieve post ID and media ID: {err}"
            }
   
    html_content = markdown.markdown(article.content, extensions=["tables"])
    soup = BeautifulSoup(html_content, "html.parser")
    
    for table in soup.find_all("table"):
        table["style"] = "border-collapse: collapse; width: 100%; border: 1px solid black;"
    for th in soup.find_all("th"):
        th["style"] = "border: 1px solid black; padding: 8px; text-align: left;"
    for td in soup.find_all("td"):
        td["style"] = "border: 1px solid black; padding: 8px;"
        
    final_html = str(soup)
    post_data = {
        "title":  unescape_amp_char(article.title),
        "content": final_html,
        "status": status,
        "excerpt": article.article_description,
        "categories": selected_categories,
        "slug": article.url_slug if article.url_slug else sanitize_url_slug(article.title.lower().replace(" ", "-")[:50])
    }

    if article.selected_featured_image:
        try:
            wp_media_id: int = post_featured_image_to_wp(
                article,
                wp_site_url,
                update_media_id=media_id if update_published_article else None
                )
            post_data.update({"featured_media": wp_media_id})

        except Exception as err:
            logger.error(f"Failed to post featured image to {wp_site_url}: {err}")

    # Fetch the wordpress routes
    routes = get_wordpress_routes(wordpress_integration.site_url)

    if not routes:
        return {"status": "error", "error_message": "Failed to fetch the wordpress rest routes."}

    # Construct the URL
    url = update_published_article and routes['posts'] + f"/{post_id}" or routes['posts']

    # Define http method
    http_method = "PUT" if update_published_article else "POST"

    try:
        HEADERS = {
            "User-Agent": random.choice(USER_AGENTS)
        }

        try:
            for attempt in range(4):  # Retry up to 4 times
                try:
                    response = requests.request(
                        method=http_method,
                        url=url,
                        json=post_data,
                        auth=(wp_admin_username, wp_app_password),
                        timeout=120,
                        headers=HEADERS
                    )
                    break  # Exit loop if request is successful

                except Exception as err:
                    time.sleep(10)
                    logger.error(err)
                    if attempt == 3:  # If it's the last attempt, re-raise the exception
                        raise

        except (requests.exceptions.ConnectionError, requests.exceptions.SSLError):
            logger.error(f"[*] Failed to connect with '{wp_site_url}'")
            return {"status": "error", "error_message": f"Failed to connect with {wp_site_url}"}

    except Exception as err:
        logger.critical(err)
        return {"status": "error", "error_message": "Internal Server Error"}

    if response.ok:
        try:
            wp_response_data: Dict = response.json()
            article_link: str = wp_response_data["link"]

            # Add article link, posted_to & mark the article as posted
            article.article_link = article_link
            article.is_posted = True
            article.posted_to = "wordpress"
            article.article_status = status == "publish" and "publish" or "draft"
            article.posted_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
            article.save()


            return {"status": "success", "article_link": article_link}

        except requests.exceptions.JSONDecodeError:
            return {"status": "error", "error_message": response.content.decode('utf-8')}

    else:
        logger.critical(f"Failed to post article on wordpress for '{user.email}' user. \
                        Wordpress status code {response.status_code}")
        return {"status": "error", "error_message": response.content.decode('utf-8')}


def publish_article_to_wf(article: Article,
                          user: User,
                          collection_id: str | None,
                          status: str = "publish",
                          publish_site: bool = True,
                          update_published_article: bool = False) -> Dict:
    """
    Used to publish an article to webflow
    :param article: Article model instance
    :param user: User model instance
    :param collection_id: Webflow CMS collection ID
    :param status: Status of the article
    :param publish_site: True/False to publish the webflow site
    :param update_published_article: True/False to update the publish article
    """
    if status not in ["publish", "draft"]:
        return {"status": "error", "error_message": "Invalid status"}

    if not update_published_article:
        webflow_integration: WebflowIntegration = article.website.webflowintegration_set.filter(collection_id=collection_id).first()
    else:
        all_integrations: QuerySet[WebflowIntegration] = article.website.webflowintegration_set.filter(site_url__isnull=False)
                
        webflow_integration = None
        article_domain = urlparse(article.article_link).netloc.replace("www.", "")
        
        for integration in all_integrations:
            raw_site_url = integration.site_url
            
            if not raw_site_url.startswith("http"):
                raw_site_url = "https://" + raw_site_url
                
            integration_domain = urlparse(raw_site_url).netloc.replace("www.", "")
            
            if article_domain == integration_domain:
                webflow_integration = integration
                break
        
    if not webflow_integration:
        webflow_integration: WebflowIntegration = article.website.webflowintegration_set.first()

    response, article_slug = publish_article_to_webflow(
        article, webflow_integration, draft=status == "draft", update_published_article=update_published_article
    )

    if response["status"] == "success":

        if not webflow_integration.collection_slug:
            article_link = f"{webflow_integration.site_url}/posts/{article_slug}"
        else:
            article_link = f"{webflow_integration.site_url}/{webflow_integration.collection_slug}/{article_slug}"

        # Add article link, posted_to & mark the article as posted
        article.article_link = article_link
        article.is_posted = True
        article.posted_to = "webflow"
        article.posted_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
        article.article_status = response["post_status"]
        article.save()

        # Publish webflow site
        if publish_site:
            publish_webflow_site(
                webflow_integration.site_id,
                webflow_integration.token,
                webflow_integration.site_url,
            )

        return {
            "status": "success",
            "article_link": article_link,
            "post_status": response.get("post_status", "publish")
        }

    else:
        return {
            "status": "error",
            "err_id": response["err_id"],
            "error_message": str(response["error_message"])
        }


def create_bulk_article_generation_jobs(article_uids: List[str], user: User,
                                        automation_project: AutomationProject = None,
                                        retry_count: int = 3,
                                        delay: int = 5,
                                        max_pod_per_node: int = 110,
                                        cluster_load_threshold: int = 75) -> Dict:
    """
    Starts bulk articles generation for given article title uids

    :param article_uids: List of article UIDs.
    :param user: User model instance.
    :param automation_project: AutomationProject model instance (defaults to None).
    :param retry_counter: Number of times to retry if there are no resources available to run the job.
    :param delay: Delay in retrying the request to k8s cluster (in seconds).
    :param max_pod_per_node: max pod per node count
    :param cluster_load_threshold: cluster load threshold limit
    """
    # check user article generation limit for the month
    current_plan_data: Dict = get_stripe_product_data(user)
    article_limit: int = current_plan_data["metadata"]["max_articles"]
    total_article_generation: int = len(article_uids)
    articles_list: List[Article] = []
    total_jobs_created: int = 0  # stores number of jobs created

    if user.articles_generated + total_article_generation > article_limit:
        logger.debug(f"[*] Max article generation limit reached for '{user.email}'.")
        return {"status": "rejected", "reason": "max_limit_reached"}

    try:
        articles_list = user.articles.filter(article_uid__in=article_uids)
    except Article.DoesNotExist:
        logger.error("no such article")

    if not articles_list:
        logger.error("no articles found")
        return {"status": "rejected", "reason": "no_articles_found"}


    # ------------------- Create K8 Job For All Articles -------------------
    for idx, article in enumerate(articles_list):

        art_gen_job_id = generate_k8_job_id(
            f"bulkarticlegeneration-{idx}", username=user.username
        )

        art_gen_data = {
            "domain": user.current_active_website.domain if user.current_active_website else "no website connected",
            "article_uid": article.article_uid,
            "title": article.title,
            "image_source": user.image_source,
            "abun_webhook_url": reverse("wh-k8-article-generation"),
            "article_tone_of_voice": user.article_tone_of_voice,
            "external_backlinks_preference": user.external_backlinks_preference,
            "article_language_preference": user.article_language_preference,           
            'tone_of_article': user.tone_of_article,
            'scale_of_tone': user.scale_of_tone,
            "max_internal_backlinks": user.max_internal_backlinks,
            "max_external_backlinks": user.max_external_backlinks,
            "ai_generated_image_style": user.ai_generated_image_style,
            "other_top_ranking_urls": article.other_top_ranking_urls,
            "article_context": article.context,
            "use_deepinfra_for_ai_img_gen": user.feature_image_template_label == "premium" and False or True,
            'toggle_table' : user.toggle_table if DEBUG else True,
            'toggle_meta_description': user.toggle_meta_description if DEBUG else True,
            'toggle_faq': user.toggle_faq if DEBUG else True,
            'toggle_tldr': user.toggle_tldr if DEBUG else True,
            'toggle_bullet_points': user.toggle_bullet_points if DEBUG else True,
        }

        if article is not None and article.keyword is not None:
            art_gen_data["keyword"] = article.keyword.keyword
        else:
            art_gen_data["keyword"] = None

        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            redis_connection.set(art_gen_job_id, json.dumps(art_gen_data))
            redis_connection.expire(art_gen_job_id, REDIS_ART_GEN_EXPIRY)

        if os.environ['K8_IMAGE_TAG'] not in ['staging', 'production']:
            machine_id = "NOT_REQUIRED"
            job_created = create_k8_job(
                art_gen_job_id,
                "article_generation_v3",
                art_gen_job_id,
                user.id,
                [art_gen_job_id],
                retry_counter=retry_count,
                delay=delay,
                max_pod_per_node=max_pod_per_node,
                cluster_load_threshold=cluster_load_threshold
            )

            if not job_created:
                logger.error(f"Failed to create article generation task for {article.article_uid}")

        else:
            # ------------------- Trigger Fly.io -------------------
            cmd = f"python3 article_gen.py {art_gen_job_id}"
            cmd = cmd.split()
            worker_props = {
                "config": {
                    "image": FLY_ARTICLE_GEN_IMAGE_URL,
                    "auto_destroy": True,
                    "init": {
                        "cmd": cmd
                    },
                    "restart": {
                        "policy": "no"
                    },
                    "guest": {
                        "cpu_kind": "shared",
                        "cpus": 1,
                        "memory_mb": 1024
                    }
                },
            }

            res = requests.post(
                f"{FLY_API_HOST}/apps/{FLY_ARTICLE_GEN_APP_NAME}/machines",
                headers={
                    'Authorization': f"Bearer {FLY_ARTICLE_GEN_DEPLOY_TOKEN}",
                    'Content-Type': 'application/json'
                },
                json=worker_props
            )

            if res.status_code != 200:
                logger.error(res.text)
                raise Exception("Failed to send the article generation task to fly.io")

            machine_id: str = res.json()['id']
            machine_name: str = res.json()['name']

            logger.debug(f"Machine ID: {machine_id}")
            logger.debug(f"Machine Name: {machine_name}")

            res = requests.get(
                f"{FLY_API_HOST}/apps/{FLY_ARTICLE_GEN_APP_NAME}/machines/{machine_id}/wait?state=started",
                headers={
                    'Authorization': f"Bearer {FLY_ARTICLE_GEN_DEPLOY_TOKEN}",
                    'Content-Type': 'application/json'
                }
            )

            if res.status_code != 200:
                raise Exception(f"Bot was unable to enter 'started' state: {res.text}")

        total_jobs_created += 1

        art_gen_k8_job = KubernetesJob(
            job_id=art_gen_job_id,
            user=user,
            status="running",
            metadata=article.article_uid,
        )
        art_gen_k8_job.save()

        # ------------------- Update article status -------------------
        article.is_processing = True

        if automation_project:
            # The newly generated articles will be attempted to be published as soon as they are generated
            article.associated_automation_project = automation_project

        article.save()

    if total_jobs_created == 0:
        return {
            "status": "error",
            "err_id": "FAILED_TO_CREATE_ARTICLE_GENERATION_TASKS",
            "reason": "failed_to_create_article_generation_tasks",
            "message": "Job is not created due to high load on the cluster."
        }

    user.articles_generated += total_jobs_created
    user.total_articles_generated += total_jobs_created
    user.save()

    return {"status": "success", "job_id": art_gen_job_id}


@transaction.atomic
def add_wix_integration(user: User, access_token: str, site_id: str, member_id: str, site_url: str | None = None):
    """
    Saves WIX integration details to user's connected website. This will also delete any existing
    cms integration data for this website.

    :param user: User model object. Integration will be added to this user.
    :param access_token: Wix access token to make authenticated requests to the API.
    :param member_id: Wix site members ID.
    :param site_id: Wix site ID
    :param site_url: Wix site url (ex. https://testing.abun.com/)
    """
    # Create the new integration
    wix_integration = WixIntegration(
        website=user.current_active_website,
        token=access_token,
        member_id=member_id,
        site_id=site_id,
        site_url=site_url,
    )
    wix_integration.save()

def get_text_node_with_optional_link(tag: bs4.Tag) -> Dict:
    '''
    Get text node and set the link
    params tag: Tag to maintain the toc link.
    '''
    if tag.name == "a" and tag.get("href"):        
        href_value = tag["href"].lstrip("#") 
        return {
            "type": "TEXT",
            "textData": {
                "text": tag.get_text(),
                "decorations": [
                    {
                        "type": "LINK",
                        "linkData": {
                            "link": {
                                "target": "SELF",
                                "url": f"#viewer-{href_value}",
                                "rel": {"nofollow": bool(tag.get("rel"))}
                            }
                        }
                    }
                ]
            }
        }
    else:
        return {
            "type": "TEXT",
            "textData": {
                "text": tag.get_text()
            }
        }

def convert_list_item(li_tag: bs4.Tag) -> Dict:
    """
    Recursively convert <li> with possible nested <ul>/<ol> inside.
    parms: toc li_tag 
    """
    child_nodes = []

    for child in li_tag.contents:
        if isinstance(child, bs4.NavigableString):
            parts = str(child).split("\\")
            for i, part in enumerate(parts):
                stripped_part = part.strip()
                if stripped_part:
                    child_nodes.append({
                        "type": "TEXT",
                        "textData": {
                            "text": stripped_part
                        }
                    })
                if i < len(parts) - 1:
                    child_nodes.append({
                        "type": "TEXT",
                        "textData": {
                            "text": "\n"
                        }
                    })
        elif isinstance(child, bs4.Tag):
            if child.name == "a":
                child_nodes.append(get_text_node_with_optional_link(child))
            elif child.name == "ol":
                child_nodes.extend(convert_ol(child))  # recurse
            else:
                child_nodes.append({
                    "type": "TEXT",
                    "textData": {
                        "text": child.get_text(strip=True).rstrip("\\")
                    }
                })

    return {
        "type": "LIST_ITEM",
        "nodes": child_nodes
    }

def convert_ol(element: bs4.Tag) -> List[Dict]:
    '''
    Converts ol to in proper structure for wix.
    :param element: bs4 element Tag
    '''
    ol_list = []
    for li in element.find_all("li", recursive=False):
        ol_list.append(convert_list_item(li))

    return [
        {
            "type": "ORDERED_LIST",
            "nodes": ol_list
        },
        {
            "type": "PARAGRAPH",
            "nodes": [{"type": "TEXT", "textData": {"text": ""}}]
        }
    ]
    
def convert_html_to_wix_nodes_structure(element: bs4.Tag) -> List:
    """
    Converts HTML content to wix node structure
    :param element: bs4 element Tag
    """
    if element.name == "p":
        anchor_tag = element.find("a")
        image_tag = element.find("img")
        strong_tag = element.find("strong")

        if not any([anchor_tag, image_tag, strong_tag]):
            return [
                {
                    "type": "PARAGRAPH",
                    "nodes": [
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": element.get_text()
                            }
                        }
                    ]
                },

                # for spacing
                {
                    "type": "PARAGRAPH",
                    "nodes": [
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": ""
                            }
                        }
                    ]
                }
            ]

        if strong_tag:
            strong_tag_text = strong_tag.get_text()
            tag_text_list = element.get_text().split(strong_tag_text)

            return [
                {
                    "type": "PARAGRAPH",
                    "nodes": [
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": tag_text_list[0]
                            }
                        },
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": strong_tag_text,
                                "decorations": [
                                    {
                                        "type": "BOLD",
                                        "fontWeightValue": 900
                                    }
                                ],
                            }
                        },
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": tag_text_list[1]
                            }
                        },
                    ]
                },

                # for spacing
                {
                    "type": "PARAGRAPH",
                    "nodes": [
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": ""
                            }
                        }
                    ]
                }
            ]

        if anchor_tag:
            anchor_tag_text = anchor_tag.get_text()
            tag_text_list = element.get_text().split(anchor_tag_text)

            return [
                {
                    "type": "PARAGRAPH",
                    "nodes": [
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": tag_text_list[0]
                            }
                        },
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": anchor_tag_text,
                                "decorations": [
                                    {
                                        "type": "LINK",
                                        "linkData": {
                                            "link": {
                                                "target": "SELF",
                                                "rel": {
                                                    "nofollow": bool(anchor_tag.get("rel"))
                                                },
                                                "url": anchor_tag.get("href")
                                            }
                                        }
                                    }
                                ]
                            }
                        },
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": tag_text_list[1]
                            }
                        },
                    ]
                },

                # for spacing
                {
                    "type": "PARAGRAPH",
                    "nodes": [
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": ""
                            }
                        }
                    ]
                }
            ]

        else:
            return [
                {
                    "type": "IMAGE",
                    "imageData": {
                        "containerData": {
                            "width": {
                                "size": "SMALL",
                            },
                            "alignment": "CENTER"
                        },
                        "image": {
                            "src": {
                                "private": False,
                                "url": image_tag.get("src")
                            },
                            "altText": image_tag.get("alt")
                        }
                    }
                },

                # for spacing
                {
                    "type": "PARAGRAPH",
                    "nodes": [
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": ""
                            }
                        }
                    ]
                }
            ]

    elif element.name in ["h1", "h2", "h3", "h4", "h5", "h6"]:
        heading_id = element.get("id")        
        heading_node = {
            "type": "HEADING",
            "headingData": {
                "level": element.name[-1],
            },
            "nodes": [
                {
                    "type": "TEXT",
                    "textData": {
                        "text": element.get_text()
                    }
                }
            ]
        }
        
        if heading_id:
            heading_node["id"] = heading_id

        return [
            heading_node,
            {
                "type": "PARAGRAPH",
                "nodes": [
                    {
                        "type": "TEXT",
                        "textData": {
                            "text": ""
                        }
                    }
                ]
            }
        ]

    elif element.name == "ul":
        ul_list = []

        for li in element:

            if li.get_text() == "\n":
                continue

            ul_list.append(
                {
                    "type": "LIST_ITEM",
                    "nodes": [
                        {
                            "type": "TEXT",
                            "textData": {
                                "text": li.get_text()
                            }
                        }
                    ]
                }
            )

        return [
            {
                "type": "BULLETED_LIST",
                "nodes": ul_list
            },

            # for spacing
            {
                "type": "PARAGRAPH",
                "nodes": [
                    {
                        "type": "TEXT",
                        "textData": {
                            "text": ""
                        }
                    }
                ]
            }
        ]

    elif element.name == "ol":
        return convert_ol(element)

    elif element.name == "table":
        table_rows = element.find_all("tr")
        headers = table_rows[0]
        rows = table_rows[1:]

        wix_table_header_nodes = []
        wix_table_row_nodes = []

        for header in headers:
            text = header.get_text()

            if text == "\n":
                continue

            wix_table_header_nodes.append(
                {
                  "type": "TABLE_CELL",
                  "nodes": [
                    {
                      "type": "PARAGRAPH",
                      "nodes": [
                        {
                          "type": "TEXT",
                          "textData": {
                            "text": text,
                          }
                        }
                      ],
                    }
                  ],
                },
            )

        for row in rows:

            table_data = []

            for data in row:
                text = data.get_text()

                if text == "\n":
                    continue

                table_data.append(
                    {
                        "type": "TABLE_CELL",
                        "nodes": [
                            {
                                "type": "PARAGRAPH",
                                "nodes": [
                                    {
                                        "type": "TEXT",
                                        "textData": {
                                            "text": text,
                                        }
                                    }
                                ],
                            }
                        ]
                    }
                )

            if table_data:
                wix_table_row_nodes.append(
                    {
                        "type": "TABLE_ROW",
                        "nodes": table_data
                    }
                )

        table_nodes = [
            {
              "type": "TABLE_ROW",
              "nodes": wix_table_header_nodes
            }
        ]

        table_nodes.extend(wix_table_row_nodes)

        return [
            {
                "type": "TABLE",
                "nodes": table_nodes,
                "tableData": {
                    "dimensions": {
                        "colsWidthRatio": [
                            370,
                            370
                        ],
                        "rowsHeight": [
                            47,
                            47
                        ],
                        "colsMinWidth": [
                            120,
                            120
                        ]
                    }
                }
            }
        ]
    else:
        raise Exception(f"Failed to convert the html content to wix nodes structure. '{element.name}' is not supported.")


def post_featured_image_to_wix(article: Article,
                               wix_integration: WixIntegration) -> Dict | None:
    """
    Posts the featured image of a given article to a WIX site. Returns the Wix file JSON response.

    :param article: Article model object.
    :param wix_integration: WixIntegration instance.
    :return: Wix file JSON response or None if the process fails.
    """
    if not article.selected_featured_image:
        logger.error(f"Error in post_featured_image_to_wix - "
                     f"No featured image available to post for article {article.article_uid}.")
        return None

    user: User = article.user

    # generate upload url
    url = "https://www.wixapis.com/site-media/v1/files/generate-upload-url"
    filename: str = f"{secrets.token_hex(32)}.{user.images_file_format}"
    headers = {
        'Content-Type': "application/json",
        'Authorization': wix_integration.token,
        'wix-site-id': wix_integration.site_id,
    }
    data = {
        'mimeType': f"image/{user.images_file_format}",
        'fileName': filename,
        'private': False,
        'labels': [],
        'externalInfo': {}
    }

    # Retry generating the upload URL up to 5 times
    max_retries = 5
    upload_url = None  # Initialize the variable before use
    for attempt in range(1, max_retries + 1):
        try:
            response = requests.post(url, headers=headers, json=data)

            if response.ok:
                upload_url = response.json().get('uploadUrl')
                if upload_url:
                    break  # Exit loop on success
            logger.error(f"Attempt {attempt}: Failed to generate upload URL -> Status {response.status_code}")
            logger.error(response.json())
        except requests.RequestException as e:
            logger.error(f"Attempt {attempt}: Exception while requesting upload URL - {e}")

        time.sleep(2)  # Wait before retrying

    if not upload_url:
        logger.critical("Exceeded maximum retries for generating upload URL.")
        return None

    # Fetch the feature image
    try:
        image_response = requests.get(article.selected_featured_image.image_url)
        if not image_response.ok:
            logger.error(f"Error in post_featured_image_to_wix - "
                        f"Failed to fetch featured image content for image url for article {article.article_uid}. "
                        f"Status Code {image_response.status_code}")
            return None
    except requests.RequestException as e:
        logger.error(f"Failed to fetch featured image for article {article.article_uid}: {e}")
        return None

    # Upload the feature image
    headers = {'Content-Type': f"image/{user.images_file_format}"}
    params = {'filename': filename}

    try:
        res = requests.put(upload_url, data=image_response.content, headers=headers, params=params)
        if not res.ok:
            logger.error(f"Failed to upload the feature image -> status code {res.status_code}")
            logger.error(res.json())
            return None
    except requests.RequestException as e:
        logger.error(f"Failed to upload the feature image for article {article.article_uid}: {e}")
        return None

    json_response = res.json()
    return json_response


def publish_article_to_wix(article: Article,
                           status: Literal["draft", "publish"] = "publish",
                           site_id: str | None = None, update_published_article: bool = False) -> Dict:
    """
    Publish an article to wix
    :param article: Article instance
    :param status: Post status
    :param site_id: Wix Site ID
    """
    
    if not update_published_article:
        wix_integration: WixIntegration = article.website.wixintegration_set.filter(site_id=site_id).first()
    else:        
        all_integrations: QuerySet[WixIntegration] = article.website.wixintegration_set.filter(site_url__isnull=False)

        wix_integration = None
        article_domain = urlparse(article.article_link).netloc.replace("www.", "")

        for integration in all_integrations:
            raw_site_url = integration.site_url
            if not raw_site_url.startswith("http"):
                raw_site_url = "https://" + raw_site_url

            integration_domain = urlparse(raw_site_url).netloc.replace("www.", "")

            if article_domain == integration_domain:
                wix_integration = integration
                break
      
    if not wix_integration:
        wix_integration: WixIntegration = article.website.wixintegration_set.first()

    url = "https://www.wixapis.com/blog/v3/draft-posts"
    headers = {
        'Content-Type': "application/json",
        'Authorization': wix_integration.token,
        'wix-site-id': wix_integration.site_id,
    }

    # get the article details
    article_title: str = unescape_amp_char(article.title)
    article_content: str = article.content
    html_content: str = markdown.markdown(article_content, extensions=["tables"])
    soup = normalize_toc_structure(html_content)
    wix_nodes_structure = []

    for element in soup.children:
        if not element.name:
            continue

        wix_nodes = convert_html_to_wix_nodes_structure(element)
        wix_nodes_structure.extend(wix_nodes)

    data = {
        "draftPost": {
            "title": article_title,
            "richContent": {
                "nodes": wix_nodes_structure
            },
            "memberId": wix_integration.member_id,
            "seoSlug": article.url_slug if article.url_slug else sanitize_url_slug(article.title.lower().replace(" ", "-")[:50])
        },
        "publish": status == "publish",
        "fieldsets": ["URL"],
    }

    # upload feature image
    feature_image_res = post_featured_image_to_wix(article, wix_integration)

    # add the featured image (if successfully uploaded)
    if feature_image_res:
        file_details = feature_image_res['file']
        data["draftPost"].update({
            "media": {
                "displayed": True,
                "custom": True,
                "wixMedia": {
                    "image": {
                        "id": file_details['id'],
                        "url": file_details['url'],
                        "altText": "Featured Image",
                    }
                }
            },
        })

    if update_published_article:
        article_id = fetch_wix_article_id(article, headers)

        if not article_id:
            logger.error("Article ID not found for update")
            return {"status": "error", "err_id": "ARTICLE_ID_NOT_FOUND", "error_message": "Article ID not found for update"}

        url = f"https://www.wixapis.com/blog/v3/draft-posts/{article_id}"
        http_method = "PATCH"
        data["action"] = "UPDATE_PUBLISH"

    else:
        url = "https://www.wixapis.com/blog/v3/draft-posts"
        http_method = "POST"

    res = requests.request(
        method=http_method,
        url=url,
        headers=headers,
        json=data,
    )

    # Add site url if request was successful and site url does not exists
    if res.ok:
        # add site url if not exists
        if not wix_integration.site_url:
            site_url = res.json()["draftPost"]["url"]["base"]
            wix_integration.site_url = site_url
            wix_integration.save()

        # get the article link
        json_data = res.json()
        article_link: str = json_data['draftPost']['url']['base'] + json_data['draftPost']['url']['path']

        # update article
        article.article_link = article_link
        article.is_posted = True
        article.posted_to = "wix"
        article.article_status = status == "publish" and "publish" or "draft"
        article.posted_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
        article.save()

        return {"status": "success", "article_link": article_link}

    else:
        return {"status": "error", "error_message": res.content}


def create_ghost_jwt(api_key: str):
    """
    Generate a valid JWT for Ghost Admin API authentication.
    :param api_key: Ghost API key
    """
    key_id, secret = api_key.split(":")
    secret = bytes.fromhex(secret)

    payload = {
        "iat": datetime.datetime.utcnow(),
        "exp": datetime.datetime.utcnow() + datetime.timedelta(minutes=5),
        "aud": "/admin/"
    }

    token = jwt.encode(payload, secret, algorithm="HS256", headers={"kid": key_id})
    return token


def fetch_ghost_article_id(article: Article, ghost_integration: GhostIntegration) -> Optional[str]:
    """
    Fetches posted article ID
    :param article: Article model instance
    :param ghost_integration: GhostIntegration model instance
    """
    url = f"{ghost_integration.site_url}/ghost/api/admin/posts/"
    jwt_token = create_ghost_jwt(ghost_integration.api_key)

    headers = {
        'Authorization': f"Ghost {jwt_token}",
        'Content-Type': "application/json"
    }

    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            posts = response.json().get('posts', [])
            article_slug = article.article_link.rstrip('/').split("/")[-1]  # Ensure no trailing slashes

            for post in posts:
                ghost_slug = post.get('slug', '')
                if ghost_slug.startswith(article_slug):  # Check if slug matches partially
                    return post.get('id')

            logger.warning(f"No matching article found for slug: {article_slug}")
            return None
        else:
            logger.error(f"Failed to fetch Ghost posts. Response: {response.text}")
            return None
    except requests.RequestException as e:
        logger.error(f"Error fetching Ghost article ID: {e}")
        return None


def post_featured_image_to_ghost(article: Article, ghost_integration: GhostIntegration) -> Optional[Dict]:
    """
    Post featured image to Ghost.
    :param article: Article model instance
    :param ghost_integration: GhostIntegration model instance
    """
    if not article.selected_featured_image:
        logger.error(f"No featured image for article {article.article_uid}")
        return None

    user = article.user
    filename = f"{secrets.token_hex(32)}.{user.images_file_format}"
    url = f"{ghost_integration.site_url}/ghost/api/admin/images/upload/"
    jwt_token = create_ghost_jwt(ghost_integration.api_key)

    headers = {'Authorization': f"Ghost {jwt_token}"}
    image_url = article.selected_featured_image.image_url

    image_response = requests.get(image_url, stream=True)
    if not image_response.ok:
        logger.error(f"Failed to fetch image. Status: {image_response.status_code}")
        return None

    files = {'file': (filename, image_response.raw, f"image/{user.images_file_format}")}

    response = requests.post(url, headers=headers, files=files)

    if not response.ok:
        logger.error(f"Failed to upload image. Response: {response.text}")
        return None

    response_json = response.json()
    if "images" in response_json and isinstance(response_json["images"], list):
        return response_json["images"][0]["url"]  # ✅ Extract only the image URL

    return None


def post_internal_image_to_ghost(article: Article,
                                 image_url: str,
                                 ghost_integration: GhostIntegration) -> Optional[str]:
    """
    Uploads an internal image to Ghost and returns the new image URL.
    :param article: Article model instance.
    :param image_url: Image URL.
    :param ghost_integration: Ghost integrations.
    """
    user = article.user
    filename = f"{secrets.token_hex(32)}.{user.images_file_format}"
    url = f"{ghost_integration.site_url}/ghost/api/admin/images/upload/"
    jwt_token = create_ghost_jwt(ghost_integration.api_key)
    headers = {'Authorization': f"Ghost {jwt_token}"}

    # Download the image
    image_response = requests.get(image_url, stream=True)
    if not image_response.ok:
        logger.error(f"Failed to fetch image. Status: {image_response.status_code}")
        return None

    # Prepare the file for upload
    files = {'file': (filename, image_response.raw, f"image/{user.images_file_format}")}

    # Upload the image
    response = requests.post(url, headers=headers, files=files)
    if not response.ok:
        logger.error(f"Failed to upload image. Response: {response.text}")
        return None

    # Extract and return the uploaded image URL
    response_json = response.json()
    if "images" in response_json and isinstance(response_json["images"], list):
        return response_json["images"][0]["url"]

    return None


def extract_image_urls(html_content: str) -> list:
    """
    Extract all internal image URLs from HTML content.
    :param html_content: HTML content
    """
    img_regex = r'<img[^>]+src="(https://cdn.abun.com/[^"]+)"'
    return re.findall(img_regex, html_content)


def replace_image_urls_in_content(html_content: str, image_url_map: dict) -> str:
    """
    Replaces all internal image URLs in the HTML content with new URLs.
    :param html_content: HTML content
    :param image_url_map: Image URL mapping.
    """
    for old_url, new_url in image_url_map.items():
        html_content = html_content.replace(old_url, new_url)
    return html_content


def convert_html_to_mobiledoc(html_content: str) -> Dict:
    """
    Converts HTML content to Ghost's Mobiledoc format.
    :param html_content: HTML content
    """
    return {
        "version": "0.3.1",
        "atoms": [],
        "cards": [["html", {"html": html_content}]],
        "markups": [],
        "sections": [[10, 0]]
    }


def publish_article_to_ghost(article: Article,
                             status: Literal["draft", "published"] = "published" ,
                             site_url: str | None = None,
                             update_published_article: bool = False) -> Dict:
    """
    Publish an article to ghost
    :param article: Article instance
    :param status: Post status
    :param site_url: Ghost Site ID
    :param update_published_article: Boolean
    """
    
    if not update_published_article:
        ghost_integration = article.website.ghostintegration_set.filter(site_url=site_url).first()
    else:
        all_integrations: QuerySet[GhostIntegration] = article.website.ghostintegration_set.filter(site_url__isnull=False)

        ghost_integration = None
        article_domain = urlparse(article.article_link).netloc.replace("www.", "")

        for integration in all_integrations:
            # Normalize and parse the integration site URL
            raw_site_url = integration.site_url
            if not raw_site_url.startswith("http"):
                raw_site_url = "https://" + raw_site_url  # Assume https if scheme is missing

            integration_domain = urlparse(raw_site_url).netloc.replace("www.", "")

            if article_domain == integration_domain:
                ghost_integration = integration
                break
    
    if not ghost_integration:
        logger.error("Ghost integration not found")
        return {"status": "error", "error_message": "Ghost integration not found"}

    url = f"{ghost_integration.site_url}/ghost/api/admin/posts/"
    jwt_token = create_ghost_jwt(ghost_integration.api_key)

    headers = {
        'Content-Type': "application/json",
        'Authorization': f"Ghost {jwt_token}",
    }

    article_title: str = unescape_amp_char(article.title)
    article_content: str = article.content
    article_description: str = article.article_description
    html_content: str = markdown.markdown(article_content)

    # Extract and upload internal images
    image_urls = extract_image_urls(html_content)
    image_url_map = {}

    for img_url in image_urls:
        new_img_url = post_internal_image_to_ghost(article, img_url, ghost_integration)
        if new_img_url:
            image_url_map[img_url] = new_img_url

    updated_html_content = replace_image_urls_in_content(html_content, image_url_map)

    mobiledoc_content = convert_html_to_mobiledoc(updated_html_content)
    feature_image_url = post_featured_image_to_ghost(article, ghost_integration)

    data = {
        "posts": [{
            "title": article_title,
            "mobiledoc": json.dumps(mobiledoc_content),
            "feature_image": str(feature_image_url) if feature_image_url else None,
            "status": status,
            "meta_description": str(article_description) if article_description else None,
            "slug": article.url_slug if article.url_slug else sanitize_url_slug(article.title.lower().replace(" ", "-")[:50])
        }]
    }

    if update_published_article:
        article_id = fetch_ghost_article_id(article, ghost_integration)
        if not article_id:
            logger.error("Article ID not found for update")
            return {"status": "error", "err_id": "ARTICLE_ID_NOT_FOUND", "error_message": "Article ID not found for update"}

        url = f"{ghost_integration.site_url}/ghost/api/admin/posts/{article_id}/"
        http_method = "PUT"

        # Fetch the existing post data to get the correct 'updated_at' timestamp
        existing_post_response = requests.get(url, headers=headers)
        if existing_post_response.ok:
            existing_post = existing_post_response.json().get("posts", [])[0]
            updated_at = existing_post.get("updated_at")
            if updated_at:
                data["posts"][0]["updated_at"] = updated_at
            else:
                logger.error("Missing 'updated_at' in the existing post data")
                return {"status": "error", "error_message": "Missing 'updated_at' field"}
        else:
            logger.error(f"Failed to fetch existing post. Response: {existing_post_response.text}")
            return {"status": "error", "error_message": "Failed to fetch existing post"}
    else:
        http_method = "POST"

    response = requests.request(method=http_method, url=url, headers=headers, json=data)

    if response.ok:
        json_data = response.json()
        article_link = json_data['posts'][0]['url']
        article.article_link = article_link
        article.is_posted = True
        article.posted_to = "ghost"
        article.article_status = status == "publish" and "publish" or "draft"
        article.posted_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
        article.save()

        return {"status": "success", "article_link": article_link}
    else:
        logger.error(f"Failed to publish article. Response: {response.text}")
        return {"status": "error", "error_message": response.text}


@transaction.atomic
def create_keyword_object(user: User, keyword: str, with_default_values: bool = False, **kwargs) -> Keyword:
    """
    Used to create keyword object
    :param user: User instance
    :param keyword: keywrod to add
    :param with_default_values: Set this to True for creating a keyword object with default values
    """
    md5_hash: str = hashlib.md5(f"{keyword}{kwargs.get('source')}{kwargs.get('country')}{uuid.uuid4().hex}".encode("utf-8")).hexdigest()

    if with_default_values:
        keyword_obj = Keyword(
            website=user.current_active_website,
            keyword_md5_hash=md5_hash,
            keyword=unescape_amp_char(keyword),
            source=kwargs["source"],
            country=kwargs["country"],
            serp_position=None,
            volume=0,
            cpc_currency="USD",
            cpc_value=0.0,
            paid_difficulty=0,
            trend={'trends': []}
        )
    else:
        keyword_obj = Keyword(
            website=user.current_active_website,
            keyword_md5_hash=md5_hash,
            keyword=unescape_amp_char(keyword),
            **kwargs
        )

    return keyword_obj


def fetch_keyword_data(new_keywords: List[str]) -> List[Dict]:
    """
    Fetch keyword data in batches of 100 from Keywords Everywhere API.
    """
    headers = {
        'Accept': 'application/json',
        'Authorization': f'Bearer {os.environ["KEYWORDSEVERYWHERE_API_KEY"]}'
    }
    new_keywords_with_data = []  # Store keyword data with additional fields

    for batch in batched(new_keywords, 100):
        payload = {
            'country': 'global',
            'currency': 'USD',
            'dataSource': 'gkp',
            'kw[]': list(batch)
        }

        try:
            res = requests.post("https://api.keywordseverywhere.com/v1/get_keyword_data", headers=headers, data=payload)  
            if res.status_code == 200:
                results = res.json()
                for keyword_data in results['data']:

                    # Handle CPC field, extract only the numeric 'value'
                    cpc_value = keyword_data['cpc']['value'] if isinstance(keyword_data['cpc'], dict) else keyword_data['cpc']

                    new_keywords_with_data.append({
                        'keyword': keyword_data['keyword'],
                        'source': 'keywordseverywhere',
                        'country': 'global',
                        'serp_position': None,
                        'volume': keyword_data['vol'],
                        'cpc': cpc_value,
                        'paid_difficulty': keyword_data['competition'],
                        'trend': keyword_data['trend'],
                    })
            # else:
            #     logger.error(f"Request to Keywords Everywhere API failed with status code {res.status_code}")
            #     raise Exception(f"Request to Keywords Everywhere API failed with status code {res.status_code}")
        except requests.exceptions.RequestException as e:
            logger.error(f"Request to Keywords Everywhere API failed: {str(e)}")
            # raise Exception(f"Error fetching keyword data: {str(e)}")

        if not new_keywords_with_data:
            results = get_keyword_volume_data_v2(new_keywords)

            for keyword_data in results:
                # Handle CPC field, extract only the numeric 'value'
                cpc_value = keyword_data['cpc']['value'] if isinstance(keyword_data['cpc'], dict) else keyword_data['cpc']

                # Convert competition to paid difficulty
                competition = keyword_data['competition']

                if competition == "LOW":
                    paid_difficulty = 0
                elif competition == "MEDIUM":
                    paid_difficulty = 0.12
                else:
                    paid_difficulty = 0.3

                new_keywords_with_data.append({
                    'keyword': keyword_data['keyword'],
                    'source': 'dataforseo',
                    'country': 'global',
                    'serp_position': None,
                    'volume': keyword_data['search_volume'],
                    'cpc': cpc_value,
                    'paid_difficulty': paid_difficulty,
                    'trend': keyword_data['monthly_searches'],
                })

    return new_keywords_with_data


def generate_visual_description_from_title(article: Article) -> str:
    """
    Generate a visual description based on article title for templates without text
    :param article: Article object
    :return: Visual description string
    """
    class VisualDescription(BaseModel):
        description: str = Field(description="Visual description of the featured image based on the article title.")

    llm = ChatOpenAI(
        model_name=get_gpt_model_name(),
        temperature=0.7,
        max_tokens=4096
    )

    # Prompt GPT to generate a visual description
    visual_description_parser = PydanticOutputParser(pydantic_object=VisualDescription)
    visual_description_prompt = PromptTemplate(
        template=generate_visual_description_prompt_text(),
        input_variables=["blog_title", "language"],
        partial_variables={"format_instructions": visual_description_parser.get_format_instructions()},
    )

    _input = visual_description_prompt.format_prompt(
        blog_title=article.title,
        language=article.website.article_language_preference
    )
    _response = llm.invoke(_input.to_string())
    _output = _response.content

    total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
    add_or_update_gpt_token_usage(total_tokens)

    structured_output = visual_description_parser.parse(_output)
    return structured_output.description


def rephrase_article_title(article: Article, max_words_allowed: int = 5) -> str:
    """
    Used to rephrase the article title to make it shorter
    :param article_title: Article title
    :parma max_words_allowed: Max words allowed. Defaults to 5
    """
    class RephraseArticleTitle(BaseModel):
        title: str = Field(description="Rephrased the article title to make it shorter.")

    if "-without-text" in article.user.feature_image_template_id or len(article.title.split(" ")) <= max_words_allowed:
        article_title = article.title

    else:
        llm = ChatOpenAI(
            model_name=get_gpt_model_name(),
            temperature=0.7,
            max_tokens=4096
        )

        # Prompt gpt to rephrase the title to make it shorter
        rephrase_parser = PydanticOutputParser(pydantic_object=RephraseArticleTitle)
        rephrase_title_prompt = PromptTemplate(
            template=rephrase_article_title_prompt_text(),
            input_variables=["blog_title", "words", "language"],
            partial_variables={"format_instructions": rephrase_parser.get_format_instructions()},
        )

        _input = rephrase_title_prompt.format_prompt(blog_title=article.title, words=random.choice([4,5,6,7]), language=article.website.article_language_preference)
        _response = llm.invoke(_input.to_string())
        _output = _response.content

        total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
        add_or_update_gpt_token_usage(total_tokens)

        structured_output = rephrase_parser.parse(_output)
        article_title = structured_output.title

    return article_title


def fetch_k8s_logs(job_id: str) -> list:
    """
    Fetch logs from KubernetesJobLogs model for the given job_id.
    :param job_id: K8 Job ID
    """
    try:
        # Query the KubernetesJobLogs associated with the given job_id
        logs = KubernetesJobLogs.objects.filter(job__job_id=job_id).order_by('created_on')

        # Concatenate all logs into a single string
        logs_output = "\n".join([f"{log.message}" for log in logs])
        return logs_output if logs else "No logs found for the given job_id."

    except Exception as e:
        return f"Error fetching logs from the database: {str(e)}"


def extract_progress_from_logs(logs: str) -> dict:
    """
    Extract the progress from k8 logs
    :param logs: K8 logs
    """
    # Initialize a dictionary to hold progress information
    progress_info = {
        'percent': 0,
        'description': 'Setting up AI...'  # Start with "Setting up AI..." at 0%
    }

    # Check if the generation is complete
    if "All Done!" in logs:
        progress_info['percent'] = 100
        progress_info['description'] = "Final Check!"
        return progress_info  # No need to check stages if already completed

    # Define expected stages in the article generation process
    stages = [
        "Starting article generation",             # Stage 1
        "Outline generation",                      # Stage 2
        "Image search generation",                 # Stage 3
        "Intro generation",                        # Stage 4
        "article body content generation",         # Stage 5
        "article body table section generation",   # Stage 6
        "Finding important words to mark as bold", # Stage 7
        "Generating F.A.Q",                        # Stage 8
        "TL;DR & Interlinking generation",         # Stage 9
        "Sending back results"                     # Stage 10
    ]

    # User-friendly names with progress intervals
    progress_stages = [
        ("Setting up AI...", 0),                 # Stage 1
        ("Sketching Ideas", 10),                 # Stage 2
        ("Researching", 20),                     # Stage 3
        ("Drafting Outline", 30),                # Stage 4
        ("Creating Content", 50),                # Stage 5
        ("Adding Table", 70),                    # Stage 6
        ("Crafting Pointers", 80),               # Stage 7
        ("Forming FAQs", 90),                    # Stage 8
        ("Almost Done!", 95),                    # Stage 9
        ("Final Check!", 99)                     # Stage 10
    ]

    # Count completed stages
    completed_stages = 0

    for index, stage in enumerate(stages):
        # Check logs for each stage's completion
        if f"[*] Running {stage}..." in logs or f"{stage} completed." in logs:
            completed_stages += 1
        elif stage in logs:
            completed_stages += 1

    # Determine the current stage and set progress info
    if completed_stages > 0:
        current_stage, progress_start = progress_stages[completed_stages - 1]
        next_stage_progress = progress_stages[completed_stages][1] if completed_stages < len(progress_stages) else 100
        progress_info['percent'] = min(int(progress_start + ((next_stage_progress - progress_start) / 2)), 100)
        progress_info['description'] = current_stage

    # Ensure progress reaches 100% when the last stage is completed
    if completed_stages == len(stages):
        progress_info['percent'] = 100
        progress_info['description'] = "Final Check!"

    return progress_info


def get_longtail_keywords(keyword: str) -> List[str]:
    """
    Used to auto suggest long tail keywords by appending letters a to z
    :param keyword: keyword to suggest long tail keywords
    """
    sorted_keywords = []  # To store suggestions in alphabetical order

    for letter in 'abcdefghijklmnopqrstuvwxyz':
        # Append each letter to the base keyword
        longtail_keyword = f"{keyword} {letter}"
        suggestions = fetch_suggestions(longtail_keyword)

        # Add suggestions to the sorted list, ensuring uniqueness
        for suggestion in suggestions:
            if suggestion not in sorted_keywords:
                sorted_keywords.append(suggestion)

    return sorted_keywords


def fetch_suggestions(keyword: str) -> List[str]:
    """
    Fetch suggestions from the Google Autocomplete API
    :param keyword: keyword to send to the API for suggestion
    :return: list of keyword suggestions
    """
    url = "http://suggestqueries.google.com/complete/search"
    params = {
        'client': 'chrome',
        'q': keyword
    }

    try:
        response = requests.get(url, params=params, timeout=10)

        if response.ok:
            suggestions = json.loads(response.text)

            # Return only the first 4 suggestions for each keyword
            return suggestions[1][:4]  # Limit to the first 4 suggestions
        else:
            logger.error(f"Failed to fetch long-tail keywords for '{keyword}'")
            return []

    except (requests.exceptions.RequestException, json.JSONDecodeError, IndexError) as e:
        logger.error(f"Error while fetching suggestions for '{keyword}': {e}")
        return []

    except Exception as e:
        logger.error(f"Unexpected error while fetching suggestions for '{keyword}': {e}")
        return []


def get_programmatic_seo_using_llm(no_of_titles: int, pattern: str, example_pattern: str):
    """
    Generates SEO-optimized titles using a language model (GPT-4o-mini) based on a given pattern and example inputs.

    Args:
        no_of_titles (int): The number of SEO-optimized titles to generate.
        pattern (str): The pattern string that defines the structure of the SEO titles, containing placeholders.
        example_pattern (str): A string that contains example patterns to guide the language model's output.

    Returns:
        SEOTitles: A parsed object containing the generated SEO titles.

    Raises:
        Exception: Any errors encountered during the execution will be raised and logged.
    """

    class SEOTitles(BaseModel):
        titles: List[str] = Field(
            description="A list of SEO-optimized titles generated based on the provided pattern and examples."
        )

    llm = ChatOpenAI(
        model_name=get_gpt_model_name(),
        temperature=0.3,
        max_tokens=16384
    )

    parser = PydanticOutputParser(pydantic_object=SEOTitles)

    prompt = PromptTemplate(
        template=get_programmatic_seo_prompt_text(),
        input_variables=["no_of_titles", "pattern", "example_pattern"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )

    _input = prompt.format_prompt(
        no_of_titles=no_of_titles,
        pattern=pattern,
        example_pattern=example_pattern
    )
    _response = llm.invoke(_input.to_string())
    _output = _response.content

    total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
    add_or_update_gpt_token_usage(total_tokens)

    structured_output = parser.parse(_output)

    return structured_output

def get_glossary_words_using_llm(word: str, no_of_words: int):
    """
    Generates Glossary Words using a language model (GPT-4o-mini) based on the keyword.

    Args:
        word: keyword provided to llm
        no_of_words (int): The number of glossary words to generate.

    Returns:
       GlossaryWords: A parsed object containing the generated GlossaryWords.

    Raises:
        Exception: Any errors encountered during the execution will be raised and logged.
    """

    class GlossaryWords(BaseModel):
        titles: List[str] = Field(
            description="A list of Glossary Words generated based on the provided keyword."
        )

    llm = ChatOpenAI(
        model_name=get_gpt_model_name(),
        temperature=0.3,
        max_tokens=4096
    )

    parser = PydanticOutputParser(pydantic_object=GlossaryWords)

    prompt = PromptTemplate(
        template=get_glossary_words_prompt_text(),
        input_variables=["word","no_of_words"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )

    _input = prompt.format_prompt(
        word = word,
        no_of_words=no_of_words,
    )

    _response = llm.invoke(_input.to_string())
    _output = _response.content

    total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
    add_or_update_gpt_token_usage(total_tokens)

    structured_output = parser.parse(_output)

    return structured_output


def get_glossary_contents_using_llm(topic: str, term: str, language: str):
    """
    Generates Glossary Content using a language model (GPT-4o-mini) based on the keyword and glossary words.

    Args:
        topic (str): The main topic or keyword for the glossary.
        term (str): A term to generate content for.

    Returns:
        dict: The generated glossary content as a dictionary with raw content.

    Raises:
        Exception: Any errors encountered during the execution will be raised and logged.
    """
    llm = ChatOpenAI(
        model_name=get_gpt_model_name(),
        temperature=0.3,
        max_tokens=4096
    )

    prompt = PromptTemplate(
        template=get_glossary_contents_prompt_text(),
        input_variables=["topic", "term", "language"]
    )

    # Format the input prompt with the word and glossary terms
    _input = prompt.format_prompt(topic=topic, term=term, language=language)
    _response = llm.invoke(_input.to_string())
    _output = _response.content.strip()

    total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
    add_or_update_gpt_token_usage(total_tokens)

    return {"content": _output}

def get_glossary_internal_link_using_llm(term: str, content: str, count: int):
    llm = ChatOpenAI(
        model_name=get_gpt_model_name(),
        temperature=0.3,
        max_tokens=4096
    )

    parser = CommaSeparatedListOutputParser()

    prompt = PromptTemplate(
        template=interlinking_prompt_text(),
        input_variables=["term", "content", "count"],
        partial_variables={"format_instructions": parser.get_format_instructions()},
    )

    chain = prompt | llm

    try:
        _response = chain.invoke({"term": term, "content": content, 'count': count})
        _output = _response.content.strip()

        total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
        add_or_update_gpt_token_usage(total_tokens)
        
        return {"content": _output}

    except Exception as e:
        logger.error(f"Error generating glossary content: {str(e)}")
        raise Exception("Failed to generate glossary content.") from e


def post_featured_image_to_shopify(article: Article, shopify_integration: ShopifyIntegration, update_published_article: bool = False) -> Dict | None:
    """
    Posts featured image of given article to Shopify shop. Returns shopify file json response.

    :param article: Article model object.
    :param shopify_integration: ShopifyIntegration model object.
    """

    try:
        image_path = article.selected_featured_image.image_url

        # Shopify header
        headers = {
                "Content-Type": "application/json",
                "X-Shopify-Access-Token": shopify_integration.access_token,
            }

        # API endpoint to upload the file
        files_api_endpoint = f"https://{shopify_integration.shop_url}/admin/api/{SHOPIFY_API_VERSION}/graphql.json"

        # GraphQL mutation query
        mutation = '''
                    mutation fileCreate($files: [FileCreateInput!]!) {
                    fileCreate(files: $files) {
                        files {
                            id
                            fileStatus
                            alt
                            createdAt
                        }
                    }
                    }
                '''
        # Variables for the GraphQL request
        variables = {
            "files": [
                {
                    "alt": "Featured Image",
                    "contentType": "IMAGE",
                    "originalSource": image_path
                }
            ]
        }
        # Prepare data for file upload
        data = {
            'query': mutation,
            'variables': variables
        }
        http_method = "POST"
        # Upload the image to Shopify
        upload_response = requests.request(
                            method=http_method,
                            url=files_api_endpoint,
                            headers=headers,
                            json=data
                        )
        # upload_response = requests.post(files_api_endpoint, json=data, headers=headers)
        if upload_response.status_code == 200:
            response = upload_response.json()

            files = response.get('data', {}).get('fileCreate', {}).get('files', [])

            if files:
                file_id = files[0].get('id')
                # Now, fetch the URL of the uploaded file via the media query
                query = f'''
                query {{
                node(id: "{file_id}") {{
                    id
                    ... on MediaImage {{
                        image {{
                                url
                            }}
                        }}
                    }}
                }}
                '''

                # Payload for the media query
                media_payload = {
                    'query': query
                }

                # wait for 3 seconds to get uploaded image url
                time.sleep(3)

                # Send the request to fetch the file URL
                media_response = requests.post(files_api_endpoint, headers=headers, json=media_payload)

                if media_response.status_code == 200:
                    media_data = media_response.json()
                    node = media_data.get('data', {}).get('node', {})
                    if node:
                        # Check for the URL
                        image_info = node.get('image', {})
                        if image_info:
                            image_url = image_info.get('url', "")
                            return image_url
        return ""
    except Exception as err:
        logger.error("Error in featured image upload on shopify :", err)
        return ""

def publish_article_to_shopify(article: Article,
                               status: Literal["draft", "publish"] = "publish",
                               shop_url: str | None = None,
                               update_published_article: bool = False) -> Dict:
    """
    Publish an article to shopify
    :param article: Article instance
    :param status: Post status
    :param shop_url: Shopify shop URL
    :param update_published_article: True/False to update the publish article
    """
    if not update_published_article:
        shopify_integration: ShopifyIntegration = article.website.shopifyintegration_set.filter(shop_url=shop_url).first()
    else:
        all_integrations: QuerySet[ShopifyIntegration] = article.website.shopifyintegration_set.filter(shop_url__isnull=False)

        shopify_integration = None
        article_domain = urlparse(article.article_link).netloc.replace("www.", "")

        for integration in all_integrations:
            raw_site_url = integration.shop_url
            if not raw_site_url.startswith("http"):
                raw_site_url = "https://" + raw_site_url

            integration_domain = urlparse(raw_site_url).netloc.replace("www.", "")

            if article_domain == integration_domain:
                shopify_integration = integration
                break
    
    if not shopify_integration:
        shopify_integration: ShopifyIntegration = article.website.shopifyintegration_set.first()


    headers = {
            "Content-Type": "application/json",
            "X-Shopify-Access-Token": shopify_integration.access_token,
        }

    # get the article details
    article_title: str = unescape_amp_char(article.title)
    article_content: str = article.content
    html_content: str = markdown.markdown(article_content, extensions=["tables"])

    soup = BeautifulSoup(html_content, "html.parser")

    for ol in soup.find_all("ol"):
        for li in ol.find_all("li"):
            for p in li.find_all("p"):
                p["style"] = "display: inline;"

    html_content = str(soup)

    # Preparing article data
    data = {
            "article": {
                "title": article_title,
                "body_html": html_content,
                "published_at": datetime.datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ') if status=="publish" else None,
                "handle": article.url_slug if article.url_slug else sanitize_url_slug(article.title.lower().replace(" ", "-")[:50])
            }
        }

    # upload feature image
    feature_image_url = post_featured_image_to_shopify(article, shopify_integration, update_published_article)

    # Add featured image src to article if image uploaded
    if feature_image_url:
        data['article'].update({"image": {
                    "src": f"{feature_image_url}"
                }})


    if update_published_article:
        article_id = get_article_id_by_handle(shop_url, headers, article)

        if not article_id:
            logger.error("Article ID not found for update")
            return {"status": "error", "err_id": "ARTICLE_ID_NOT_FOUND", "error_message": "Article ID not found for update"}

        url = f"https://{shop_url}/admin/api/{SHOPIFY_API_VERSION}/articles/{article_id}.json"
        http_method = "PUT"

    else:
        url = f"https://{shop_url}/admin/api/{SHOPIFY_API_VERSION}/articles.json"
        http_method = "POST"

    try:
        response = requests.request(
            method = http_method,
            url = url,
            headers=headers,
            json=data
        )

    except Exception as err:
        logger.critical(err)
        return {"status": "error", "error_message": "Failed to post the article"}

    if response.status_code == 201 or response.status_code == 200:
        response = response.json()

        try:
            # Construct the article URL
            handle = response['article']['handle']
            article_link: str = f"https://{shop_url}/blogs/news/{handle}"
        except KeyError as key:
            logger.error(f"KeyError occurred while processing the articles: {key}")
            logger.error(response)

            # Set article_link to empty string if handle is not found
            article_link = ""

        # update article
        article.article_link = article_link
        article.is_posted = True
        article.posted_to = "shopify"
        article.article_status = status == "publish" and "publish" or "draft"
        article.posted_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
        article.save()

        return {"status": "success", "article_link": article_link}

    else:
        return {"status": "error", "error_message": response.content}


def get_wp_postid_media_id(article, wp_site_url, wp_admin_username, wp_app_password):
    """
    Get the article post id and media id for update published article

    Args:
        article: An object containing the article's `post_link`.
        wp_site_url: The WordPress site URL.
        wp_admin_username: The WordPress admin username.
        wp_app_password: The WordPress application password.

    Returns:
        A tuple `(post_id, media_id)` if successful, or a dictionary with an error message.
    """
    # Fetch the wordpress routes
    routes = get_wordpress_routes(wp_site_url)

    if not routes:
        return {"status": "error", "error_message": "Failed to fetch the wordpress rest routes."}

    try:
        HEADERS = {
            "User-Agent": random.choice(USER_AGENTS)
        }

        post_link = article.article_link
        slug = post_link.rstrip("/").split("/")[-1]
        response = requests.get(f"{routes['posts']}?slug={slug}",
                                auth=(wp_admin_username, wp_app_password),
                                headers=HEADERS)

        if response.status_code != 200:
            logger.error(f"Failed to fetch post data. Status code: {response.status_code}")
            return {"status": "error", "error_message": f"Failed to fetch post data. HTTP {response.status_code}"}

        post_data = response.json()

        if not post_data:
            logger.error(f"No post found for slug '{slug}'")
            return {"status": "error", "error_message": "No post found for the provided slug"}

        post_id = post_data[0].get("id")
        media_id = post_data[0].get("featured_media")

        return post_id, media_id

    except (requests.exceptions.ConnectionError, requests.exceptions.SSLError):
            logger.critical(f"[*] Failed to connect with '{wp_site_url}'")
            return {"status": "error", "error_message": f"Failed to connect with {wp_site_url}"}

    except Exception as err:
        logger.critical(err)
        return {"status": "error", "error_message": "Internal Server Error"}

def get_wp_postid_media_id_glossary(glossary, wp_site_url, wp_admin_username, wp_app_password):
    """
    Get the article post id and media id for update published article

    Args:
        glossary: An object containing the glossary's `post_link`.
        wp_site_url: The WordPress site URL.
        wp_admin_username: The WordPress admin username.
        wp_app_password: The WordPress application password.

    Returns:
        A tuple `(post_id, media_id)` if successful, or a dictionary with an error message.
    """
    # Fetch the wordpress routes
    routes = get_wordpress_routes(wp_site_url)

    if not routes:
        return {"status": "error", "error_message": "Failed to fetch the wordpress rest routes."}

    try:
        HEADERS = {
            "User-Agent": random.choice(USER_AGENTS)
        }

        post_link = glossary.glossary_link
        slug = post_link.rstrip("/").split("/")[-1]
        response = requests.get(f"{routes['posts']}?slug={slug}",
                                auth=(wp_admin_username, wp_app_password),
                                headers=HEADERS)

        if response.status_code != 200:
            logger.error(f"Failed to fetch post data. Status code: {response.status_code}")
            return {"status": "error", "error_message": f"Failed to fetch post data. HTTP {response.status_code}"}

        post_data = response.json()

        if not post_data:
            logger.error(f"No post found for slug '{slug}'")
            return {"status": "error", "error_message": "No post found for the provided slug"}

        post_id = post_data[0].get("id")
        media_id = post_data[0].get("featured_media")

        return post_id, media_id

    except (requests.exceptions.ConnectionError, requests.exceptions.SSLError):
            logger.critical(f"[*] Failed to connect with '{wp_site_url}'")
            return {"status": "error", "error_message": f"Failed to connect with {wp_site_url}"}

    except Exception as err:
        logger.critical(err)
        return {"status": "error", "error_message": "Internal Server Error"}


def fetch_appsumo_access_token(appsumo_code: str) -> None | Dict:
    """
    Used to fetch the temporary appsumo access token.
    :parma appsumo_code: AppSumo code
    """
    url = 'https://appsumo.com/openid/token/'
    headers = {'Content-type': 'application/json'}
    data = {
        'client_id': APPSUMO_CLIENT_ID,
        'client_secret': APPSUMO_CLIENT_SECRET,
        'code': appsumo_code,
        'redirect_uri': f'{WP_RETURN_URL_DOMAIN}/auth/appsumo/signup',
        'grant_type': 'authorization_code'
    }

    response = requests.post(url, headers=headers, json=data)
    json_response = response.json()

    if not response.ok:
        logger.error(
            f"Failed to fetch the temporary access token from appsumo. Status code: {response.status_code} " \
            f"-> {json_response}"
        )
        return None

    return json_response


def fetch_appsumo_user_license_key(access_token: str, refresh_token: str) -> str | None:
    """
    Fetches the appsumo user license key using `access_token`
    :param access_token: Appsumo accces token
    :param refresh_token: Appsumo refresh token
    """
    response = requests.get(f'https://appsumo.com/openid/license_key/?access_token={access_token}')
    json_data = response.json()

    if response.ok:
        license_key = json_data['license_key']
        return license_key

    elif response.status_code == 401:
        url = 'https://appsumo.com/openid/token/'
        headers = {'Content-type': 'application/json'}

        data = {
            'client_id': APPSUMO_CLIENT_ID,
            'client_secret': APPSUMO_CLIENT_SECRET,
            'refresh_token': refresh_token,
            'grant_type': 'refresh_token'
        }

        response = requests.post(url, headers=headers, json=data)
        json_data = response.json()
        return json_data['license_key']

    else:
        logger.error(
            f"Failed to fetch the license key. Status Code: {response.status_code} Response: {json_data}"
        )

    return None


def fetch_webflow_cms_collection_item(wf_site_url: str, collection_id: str, headers: dict, retries: int = 3, delay: int = 10):
    """
    Fetch the CMS collection item ID from a Webflow site.

    This function retrieves the CMS collection item ID by matching the `slug` of an item in the
    specified collection with the `data-wf-item-slug` extracted from the Webflow site URL.

    Args:
        wf_site_url (str): The Webflow site URL to extract the slug from.
        collection_id (str): The ID of the Webflow CMS collection to search.
        headers (dict): The request headers, including the Webflow API key.

    Returns:
        str: The ID of the matching CMS collection item if found.
        None: If no matching item is found or an error occurs.

    Raises:
        ValueError: If the collection ID or site URL is invalid, or the API response is unexpected.
        requests.exceptions.RequestException: For any issues during the API call.
    """
    try:
        url = f"https://api.webflow.com/v2/collections/{collection_id}/items"

        # Fetch the slug from the provided Webflow site URL
        if wf_site_url:
            slug_to_find = wf_site_url.split("/")[-1]

        if not slug_to_find:
            raise ValueError("Failed to extract slug from the Webflow site URL.")

        response = requests.get(url, headers=headers)
    
        if response.status_code == 429:
            time.sleep(delay)
            delay += 10
            retries -= 1
            return fetch_webflow_cms_collection_item(wf_site_url, collection_id, headers, retries, delay)

        if response.status_code != 200:
            raise ValueError(f"Failed to fetch items: {response.status_code}, {response.text}")

        data = response.json()
        items = data.get("items", [])

        for item in items:
            field_data = item.get("fieldData", {})
            if field_data.get("slug") == slug_to_find:
                return item.get("id")

        # Return None if no matching item is found
        return None

    except requests.exceptions.RequestException as e:
        logger.error(f"Network error while fetching items: {e}")
        return None

    except ValueError as ve:
        logger.error(f"Error: {ve}")
        return None

    except Exception as e:
        logger.critical(f"An unexpected error occurred: {e}")
        return None


def get_article_id_by_handle(shop_url: str, headers: dict, article) -> int | None:
    """
    Retrieves the Shopify article ID based on the handle extracted from the article's link.

    :param shop_url: Shopify shop URL.
    :param headers: HTTP headers containing the Shopify Access Token.
    :param article: Article object that includes an article_link attribute.
    :return: The article ID if found, otherwise None.
    """
    try:
        # Build the URL to fetch all articles
        articles_url = f"https://{shop_url}/admin/api/{SHOPIFY_API_VERSION}/articles.json"
        # Extract handle from the article's link
        handle = article.article_link.split("/")[-1].lower()

        # Make a request to fetch articles
        response = requests.get(articles_url, headers=headers)
        # Check if the response is successful
        if response.status_code == 200:
            articles = response.json().get("articles", [])

            # Search for the article with the matching handle
            for article_item in articles:
                if article_item["handle"] == handle:
                    return article_item["id"]
            return None
        else:
            logger.error(f"Failed to fetch articles. HTTP Status: {response.status_code}, Response: {response.content}")
            return None
    except KeyError as ke:
        logger.critical(f"KeyError occurred while processing the articles: {ke}")
        return None
    except AttributeError as ae:
        logger.critical(f"AttributeError: Ensure 'article' has a valid 'article_link' attribute. Details: {ae}")
        return None
    except requests.RequestException as re:
        logger.critical(f"RequestException occurred while connecting to Shopify: {re}")
        return None
    except Exception as ex:
        logger.critical(f"An unexpected error occurred: {ex}")
        return None


def fetch_wix_article_id(article, headers):
    """
    Fetch the Wix article ID for a given article.

    This function retrieves the Wix article ID by matching the `seoSlug` of the article
    with the slug extracted from the provided article link.

    Args:
        article (object): An object containing `article_link`, which is the URL of the article.
        headers (dict): Headers required for authentication, including Wix API keys.

    Returns:
        str: The article ID if found.
        None: If no matching article is found or if an error occurs.

    Raises:
        Exception: If the API request fails with a non-200 status code.
    """
    url = "https://www.wixapis.com/blog/v3/draft-posts/"
    try:
        slug = article.article_link.split("/")[-1]  # Extract the slug from the article link
        response = requests.get(url, headers=headers)
        # Check if the API call was successful
        if response.status_code == 200:
            sites = response.json().get('draftPosts', [])
            for site in sites:
                if site.get('seoSlug') == slug:
                    return site.get('id')
            return None  # No matching article found
        else:
            # Log or raise an exception for non-200 status codes
            raise Exception(f"Failed to fetch draft posts. Status Code: {response.status_code}, "
                            f"Response: {response.json()}")
    except requests.RequestException as e:
        # Handle any request-related exceptions (e.g., network issues)
        raise Exception(f"An error occurred while fetching the Wix article ID: {e}")

def get_automation_projects_with_articles(automation_projects):
    """
    Serialize AutomationProject data and append published_article_set.
    """

    serialized_data = AutomationProjectSerializer(automation_projects, many=True).data

    for project, serialized_project in zip(automation_projects, serialized_data):
        published_articles = getattr(project, 'published_article_set', [])
        serialized_project['publishedArticleSet'] = ArticleTitleTableDataSerializer(published_articles, many=True).data

    return serialized_data


def format_ltd_plan_name(tiers: List):
    """
    Format LTD plan name based on number of tiers
    :param tiers: LTD plan tiers user have
    """
    if not tiers or tiers == [None]:
        return None

    if len(tiers) == 1:
        return f"LTD: Tier {tiers[0]}"

    elif len(tiers) == 2:
        return f"LTD: Tiers {tiers[0]} & {tiers[1]}"

    else:
        tiers = list(map(str, tiers))
        return f"LTD: Tiers {', '.join(str(tiers[:-1]))} & {tiers[-1]}"


def sanitize_text(text: str) -> str:
    """
    Remove null bytes and other problematic characters from text
    :param text: Text which required sanitization.
    """
    if not text:
        return ""

    # Remove null bytes
    text = text.replace('\x00', '')

    # Remove other control characters except newlines and tabs
    text = ''.join(char for char in text if char >= ' ' or char in '\n\t')

    return text


def get_next_renewal_date():
    """
    Used to get the next renewal date
    """
    current_time = datetime.datetime.now(tz=ZoneInfo("UTC"))
    return current_time + relativedelta(days=30)


def find_email(fullname,domain):
    try:
        headers = {"accept": "application/json"}
        mailer_url = f"https://mailer.draftss.com/find-email?fname={fullname[0]}&lname={fullname[-1]}&domain={domain}"
        response = requests.get(mailer_url,headers=headers)
        if response.status_code == 200:
            json_res = response.json()
            if json_res["address"][0]:
                return json_res["address"][0]
            else:
                return json_res["message"]
    except Exception as e:
        logger.error(e)


def extract_blog_urls(file_path, header) -> list:
    """
    Extract blog URLs from the first column of a CSV or Excel file.

    Args:
        file_path (str): Path to the file (CSV or Excel).

    Returns:
        list: A list of blog URLs from the file.
    """
    try:
        # Determine file type and read accordingly
        file_name = file_path.name
        if file_name.endswith('.csv'):
            df = pd.read_csv(file_path)
        elif file_name.endswith(('.xls', '.xlsx')):
            df = pd.read_excel(file_path)
        else:
            raise ValueError("Unsupported file type. Please provide a .csv, .xls, or .xlsx file.")

        # Check if 'blog_urls' column exists
        if len(df) > 200:
            raise ValueError("File contains more than 200 rows. Please provide a file with 200 rows or fewer.")

        blog_urls = df[header].dropna().drop_duplicates().tolist()

        return blog_urls
    except Exception as e:
        logger.error(f"Error reading file: {e}")
        return []


def create_blog_hash(**kwargs):
    """
    Unique hash for blog urls
    """
    def random_string(length=8):
        """Generate a random string of the specified length."""
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

    m = hashlib.md5()
    random_part = random_string()
    m.update(
        str(random_part + kwargs["country"] + kwargs["source"]).encode("utf-8")
    )
    md5_hash: str = m.hexdigest()

    # make sure the keyword_md5_hash is unique
    counter = 1
    while BlogFinder.objects.filter(blog_m5_hash=md5_hash).exists():
        m = hashlib.md5()
        random_part = random_string()
        m.update(
            str(random_part + kwargs["country"] + kwargs["source"] + f"{counter}").encode("utf-8")
        )
        md5_hash: str = m.hexdigest()
        counter += 1
    return md5_hash


def process_blog_url(blog_url, country, website, user: User):
    """
    Process a single blog URL to check validity, find the author, and fetch the email.
    """
    try:
        is_valid, author_name = check_blog_link(blog_url)
        fullname = author_name.strip().split()
        parsed_url = urlparse(blog_url)
        domain = parsed_url.netloc
        email_address = find_email(fullname, domain) if len(fullname) >= 2 else "No email"

        if is_valid:
            blog_m5_hash = create_blog_hash(country=country, source="user-blog")
            if email_address not in ["No email", "This recipient_domain uses a catch-all mailbox", "No emails found for this person"]:
                user.blog_emails_found += 1
                user.total_blog_emails_found += 1
                user.save()

            return BlogFinder(
                website=website,
                blog_url=blog_url,
                is_valid=is_valid,
                author_name=author_name,
                email_address=email_address,
                blog_m5_hash=blog_m5_hash,
            )
    except Exception as e:
        logger.error(f"Error processing blog URL {blog_url}: {e}")
    return None


def publish_glossary_to_wp(
        glossary: GlossaryContent,
        user: User,
        status: str = "publish",
        wp_site_url: str | None = None,
        selected_categories: str = None,
        update_published_article: bool = False,
) -> Dict:
    """
    Used to publish an glossary content to wordpress
    :param glossary: GlossaryContent model instance
    :param user: User model instance
    :param status: Status of the glossary
    """
    wordpress_integration: WordpressIntegration = glossary.website.wordpressintegration_set.filter(site_url=wp_site_url).first()

    if not wordpress_integration:
        wordpress_integration: WordpressIntegration = glossary.website.wordpressintegration_set.first()

    wp_site_url: str = wordpress_integration.site_url
    wp_admin_username: str = wordpress_integration.user_login
    wp_app_password: str = wordpress_integration.password

    if update_published_article:
        try:
            post_id, _ = get_wp_postid_media_id_glossary(glossary, wp_site_url, wp_admin_username, wp_app_password)
        except Exception as err:
            logger.error(f"Failed to retrieve post ID and media ID from WordPress: {err}")
            return {
                "status": "error",
                "error_message": f"Failed to retrieve post ID and media ID: {err}"
            }

    post_data = {
        "title":  unescape_amp_char(glossary.term),
        "content": markdown.markdown(glossary.content),
        "status": status,
        "excerpt": glossary.content,
        "categories": selected_categories,
    }

    # Fetch the wordpress routes
    routes = get_wordpress_routes(wp_site_url)

    if not routes:
        return {"status": "error", "error_message": "Failed to fetch the wordpress rest routes."}

    # Construct the URL
    url = update_published_article and routes['posts'] + f"/{post_id}" or routes['posts']

    # Define http method
    http_method = "PUT" if update_published_article else "POST"

    try:
        HEADERS = {
            "User-Agent": random.choice(USER_AGENTS)
        }

        try:
            response = requests.request(
                method=http_method,
                url=url,
                json=post_data,
                auth=(wp_admin_username, wp_app_password),
                headers=HEADERS
            )

        except (requests.exceptions.ConnectionError, requests.exceptions.SSLError):
            logger.error(f"[*] Failed to connect with '{wp_site_url}'")
            return {"status": "error", "error_message": f"Failed to connect with {wp_site_url}"}

    except Exception as err:
        logger.critical(err)
        return {"status": "error", "error_message": "Internal Server Error"}

    if response.ok:
        try:
            wp_response_data: Dict = response.json()
            glossary_link: str = wp_response_data["link"]

            # Add article link, posted_to & mark the article as posted
            glossary.glossary_link = glossary_link
            glossary.is_posted = True
            glossary.posted_to = "wordpress"
            glossary.posted_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
            glossary.save()


            return {"status": "success", "glossary_link": glossary_link}

        except requests.exceptions.JSONDecodeError:
            return {"status": "error", "error_message": response.content}

    else:
        return {"status": "error", "error_message": response.content}


def get_hypestat_results(args):
    """
    Function to get Website Traffic and Website Authority via HypeState API
    :param url: Website URL for Scrap data

    :returns: HypeState API Response for requested URL in json form
    """
    website, result = args
    url = result['link']
    if url:
        domain = re.sub(r'^https?://(www\.)?', '', url).split('/')[0]

       # Use get_or_create to avoid duplicate domain error
        hypestat_data, created = HypestatData.objects.get_or_create(website=website, domain=domain)

        if created:  # If the data was created (new domain)
            try:
                res = requests.get(f"https://wjoazptcqycbxodkxmgkgr2yb40qrfpz.lambda-url.us-east-1.on.aws/?domain={domain}")
                if res.status_code == 200:
                    res_json = res.json()
                    hypestat_data.organic_traffic = res_json['organic_traffic']
                    hypestat_data.organic_keywords = res_json['organic_keywords']
                    hypestat_data.domain_authority = res_json['domain_authority']
                    hypestat_data.total_backlinks = res_json['total_backlinks']
                    hypestat_data.follow = res_json['follow']
                    hypestat_data.no_follow = res_json['no_follow']
                    hypestat_data.referring_domains = res_json['referring_domains']

                    hypestat_data.save()  # Save the updated data
                    result['hype'] = hypestat_data
                    return result
            except Exception as e:
                logger.error(f"Error fetching data for domain {domain}: {e}")

        else:
            try:
                hype = HypestatData.objects.get(website=website, domain=domain)
                result['hype'] = hype
                return result
            except Exception as err:
                logger.error(err)

        result['hype'] = None
        return result


def get_guest_post_data(response: requests.Response, guest_post_finder: GuestPostFinderQuery, website: Website) -> int:
    """
    Function to get/filter data from Google search API Responce and HypeState API
    :param response: Google Search API Responce
    """

    if response.status_code == 200:
        response = response.json()
        results = response.get("organic", [])

        bulk_results = []
        pool = ThreadPoolExecutor(max_workers=10)
        for post in pool.map(get_hypestat_results, [(website, result) for result in results]):
            hypestat = post.get('hype')  # Get the hypestat data

            # Check if hypestat is available
            if hypestat:              
                bulk_results.append(GuestPostFinderResult(
                    post_title=unescape_amp_char(post.get('title', '')),
                    post_link=post.get('link', ''),
                    guest_post_finder=guest_post_finder,
                    hypestat=hypestat
                ))                
            else:
                logger.warning(f"No hypestat data found for {post.get('link')}")
        # logger.info(f"Total processed: {attempted}")
        if bulk_results:
            GuestPostFinderResult.objects.bulk_create(bulk_results, batch_size=100)
            
        logger.info(f"Total GuestPostFinderResult created: {len(bulk_results)}")
        # logger.info(f"Total skipped due to missing hypestat: {attempted - titles_generated}")

    return len(bulk_results)

def get_reddit_post_data(response: requests.Response, reddit_post_finder: RedditPostFinderQuery) -> int:
    """
    Function to get/filter data from Google search API Response.
    :param response: Google Search API Response
    :param reddit_post_finder: RedditPostFinderQuery instance.
    """

    titles_generated = 0

    if response.status_code == 200:
        response = response.json()
        results = response.get("organic", [])

        for post in results:
            link = post.get('link', '').strip()
            position = post.get('position', '')
            if 'reddit.com' in link and '/comments/' in link:  # Ensure it's a Reddit 
                reddit_extra = fetch_additional_reddit_data(link)
                if reddit_extra.get("error"):
                    logger.error(f"Reddit fetch error: {reddit_extra['error']}")
                                        
                try:
                    # Extract and clean the title
                    original_title = post.get('title', '').strip()
                    # Remove everything after the first " - ", remove "r/", and "..."
                    # cleaned_title = re.sub(r"-.*$", "", original_title)  # Remove content after " - "
                    cleaned_title = re.sub(r"\s*-\s*Reddit$", "", original_title, flags=re.IGNORECASE) 
                    cleaned_title = re.sub(r"\br/\S+", "", cleaned_title)  # Remove "r/" and subreddit mentions
                    cleaned_title = re.sub(r"\.\.\.", "", cleaned_title)  # Remove "..."
                    cleaned_title = re.sub(r":", "", cleaned_title)  # Remove ":"
                    cleaned_title = cleaned_title.strip()  # Remove leading/trailing spaces
                    title = reddit_extra.get("post_title") if reddit_extra.get("post_title") else cleaned_title
                    
                    # Save the cleaned title, link, and position
                    RedditPostFinderResult.objects.create(
                        post_title=title,
                        post_link=link,
                        reddit_post_finder=reddit_post_finder,
                        position=position,
                        subreddit_name=reddit_extra.get("subreddit_name") or "",
                        upvote_score=reddit_extra.get("score") or 0,
                        upvote_ratio=reddit_extra.get("upvote_ratio") or 0.0,
                        num_comments=reddit_extra.get("num_comments") or 0,
                        created_utc=reddit_extra.get("created_utc"),
                        subreddit_subscribers=reddit_extra.get("subreddit_subscribers") or 0,
                        reddit_content=reddit_extra.get("selftext") or ""
                    )
                    titles_generated += 1
                except IndexError:
                    continue  # Skip malformed URLs
            else:
                logger.warning(f"Invalid Reddit post link: {post.get('link')}")

    return titles_generated


def get_gsc_keyword_status_by_keyword(user: User, keyword: str) -> GSCKeywordStatus:
    """
    Fetches the GSCKeywordStatus for a given keyword.
    :param keyword: The keyword to fetch status for
    :return: GSCKeywordStatus object or None if not found
    """
    try:
        # Fetching the GSCKeywordStatus object by keyword
        gsc_keyword_status = GSCKeywordStatus.objects.filter(
            keyword=keyword,
            website=user.current_active_website
            ).order_by('-created_at').first()
        return gsc_keyword_status
    except GSCKeywordStatus.DoesNotExist:
        # If keyword status does not exist, return None
        return None


def fetch_wordpress_rest_route_details(site_url: str) -> str | None:
    """
    Used to fetch the wordpress rest route details.
    :param site_url: Wordpress site URL
    """

    HEADERS = {
        "User-Agent": random.choice(USER_AGENTS)
    }

    def fetch(url: str) -> str | None:
        try:
            res = requests.get(url + "/?rest_route=/", headers=HEADERS, timeout=60)
            if not res.ok:
                logger.error(f"Failed to fetch the WordPress 'rest_route' for {url}")
                return None
            data = res.json()
            return data
        except Exception as err:
            logger.error(err)
            return None

    # Try fetching with the original site_url
    content = fetch(site_url)
    if content is not None:
        return content

    # If the site_url starts with "http" or "https", remove it
    if site_url.startswith("http") or site_url.startswith("https"):
        site_url = site_url.replace("https://", "").replace("http://", "")

    # If the first attempt fails, try without "www"
    if site_url.startswith("www."):
        site_url_without_www = site_url[4:]  # Remove "www."
        content = fetch("https://" + site_url_without_www)
        if content is not None:
            return content

    # If the first attempt fails, try with "www" if it was not present
    if not site_url.startswith("www."):
        site_url_with_www = "www." + site_url  # Add "www."
        content = fetch("https://" + site_url_with_www)
        if content is not None:
            return content

    return None


@lru_cache(maxsize=10, typed=True)
def get_wordpress_routes(site_url: str) -> None | Dict:
    """
    Returns wordpress all required routes.
    :param site_url: Wordpress Site URL
    """
    rest_routes = fetch_wordpress_rest_route_details(site_url)

    if not rest_routes:
        return None

    try:
        routes = {
            'site_url': rest_routes['url'],
            'user_and_password_authorization': rest_routes.get('routes', {}).get('/wp/v2/users/me', {}).get('_links', {}).get('self', [{}])[0].get('href'),
            'categories': rest_routes.get('routes', {}).get('/wp/v2/categories', {}).get('_links', {}).get('self', [{}])[0].get('href'),
            'media': rest_routes.get('routes', {}).get('/wp/v2/media', {}).get('_links', {}).get('self', [{}])[0].get('href'),
            'posts': rest_routes.get('routes', {}).get('/wp/v2/posts', {}).get('_links', {}).get('self', [{}])[0].get('href'),
            'redirect_authorization': f"{rest_routes['url']}/wp-admin/authorize-application.php",

        }

    except (TypeError, IndexError, KeyError):
        return None

    return routes


def limit_tokens(text: str, max_tokens: int) -> str:
    """
    Limit text to a maximum number of tokens.
    This is a simple approximation - 1 token is roughly 4 characters in English.

    :param text: Text to limit
    :param max_tokens: Maximum number of tokens
    :return: Limited text
    """
    # Simple approximation: 1 token ≈ 4 characters for English text
    char_limit = max_tokens * 4

    if len(text) <= char_limit:
        return text

    # If text exceeds limit, truncate it
    return text[:char_limit]


def limit_conversation_history(conversation_history: List, max_tokens: int) -> str:
    """
    Limit conversation history to a maximum number of tokens.
    Removes oldest conversations if the history exceeds the token limit.

    :param conversation_history: Conversation history text
    :param max_tokens: Maximum number of tokens
    :return: Limited conversation history
    """
    # Simple approximation: 1 token ≈ 4 characters for English text
    char_limit = max_tokens * 4

    if len(conversation_history) <= char_limit:
        return conversation_history

    conversations = conversation_history

    # Keep removing oldest conversations until we're under the limit
    while len("\n\n".join(conversations)) > char_limit and len(conversations) > 1:
        conversations.pop(0)  # Remove the oldest conversation

    # If we still exceed the limit with just one conversation, truncate it
    if len("\n\n".join(conversations)) > char_limit:
        return limit_tokens("\n\n".join(conversations), max_tokens)

    return "\n\n".join(conversations)


def encrypt_dict(data: dict) -> str:
    """
    Encrypt a dictionary using a predefined secret key.
    :param data: Dictionary data.
    """
    key = base64.urlsafe_b64encode(hashlib.sha256(SECRET_KEY.encode()).digest())
    f = Fernet(key)
    json_data = json.dumps(data)  # Convert dict to JSON string
    encrypted = f.encrypt(json_data.encode())  # Encrypt the data
    return encrypted.decode()


def decrypt_dict(encrypted_data: str) -> dict:
    """
    Decrypt an encrypted dictionary using the predefined secret key.
    :param encrypted_data: Encrypted dictionary data.
    """
    key = base64.urlsafe_b64encode(hashlib.sha256(SECRET_KEY.encode()).digest())
    f = Fernet(key)
    decrypted = f.decrypt(encrypted_data.encode()).decode()  # Decrypt the data
    return json.loads(decrypted)  # Convert back to dictionary


def get_table_of_content(language: str, complete_article_markdown: str) -> Optional[BaseModel]:
    """Generate Table of Contents from Markdown article content."""

    # Input validation
    if not complete_article_markdown or not complete_article_markdown.strip():
        logger.error("TOC generation skipped: Article content is empty")
        return None

    if not language or not language.strip():
        logger.error("TOC generation: Language not specified, defaulting to 'English'")
        language = "English"

    class TOCItem(BaseModel):
        title: str
        link: str
        subtopics: list["TOCItem"] = []

    class TableOfContent(BaseModel):
        toc: list[TOCItem]

    llm = ChatOpenAI(
        model_name=get_gpt_model_name(),
        temperature=0.3,
        max_tokens=4096
    )

    toc_parser = PydanticOutputParser(pydantic_object=TableOfContent)

    prompt = PromptTemplate(
        template=table_of_content_prompt_text(),
        input_variables=["language", "complete_article_markdown"],
        partial_variables={"format_instructions": toc_parser.get_format_instructions()}
    )

    _input = prompt.format_prompt(
        language=language,
        complete_article_markdown=complete_article_markdown,
    )

    try:
        _response = llm.invoke(_input.to_string())

        # Check if response is None
        if _response is None:
            logger.error("TOC generation failed: LLM response is None")
            return None

        _output = _response.content

        # Check if response content is None or empty
        if _output is None:
            logger.error("TOC generation failed: LLM response content is None")
            return None

        if not _output.strip():
            logger.error("TOC generation failed: LLM response content is empty")
            return None

        # Log the raw output for debugging
        logger.info(f"TOC raw LLM output: {_output}...")

        total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
        add_or_update_gpt_token_usage(total_tokens)

        structured_output = toc_parser.parse(_output.strip())

        # Validate the parsed output
        if structured_output and hasattr(structured_output, 'toc'):
            logger.debug(f"TOC generation successful: Found {len(structured_output.toc)} items")
            return structured_output
        else:
            logger.error("TOC generation: Parsed output is invalid or has no toc attribute")
            return None

    except langchain.schema.OutputParserException as e:
        logger.error(f"TOC parsing failed with OutputParserException: {e}")
        logger.error(f"Failed to parse output: {_output if '_output' in locals() else 'No output available'}")
        return None

    except ValidationError as e:
        logger.error(f"TOC parsing failed with ValidationError: {e}")
        logger.error(f"Failed to validate output: {_output if '_output' in locals() else 'No output available'}")
        return None

    except Exception as e:
        logger.error(f"Unexpected error while parsing TOC: {e}")
        logger.error(f"Error occurred with output: {_output if '_output' in locals() else 'No output available'}")
        return None


def slugify(text: str):
    """Converts heading text to a valid Markdown anchor format."""
    text = re.sub(r"\*\*|\d+\.\s*", "", text)  # Remove Markdown bold and leading numbers
    return re.sub(r"[^\w\s-]", "", text.lower().strip()).replace(" ", "-")  # Clean and replace spaces

def remove_leading_number(title: str) -> str:
    """Remove leading number + dot (e.g., '1. ') from a title."""
    return re.sub(r'^\s*\d+\.\s*', '', title)

def format_toc_markdown(toc):
    """Converts a Table of Contents object into a Markdown format."""
    if not toc or not hasattr(toc, 'toc'):
        return "## Table of Contents\n\n*No TOC available*"

    toc_md = "## Table of Contents\n"
    for index, item in enumerate(toc.toc, start=1):
        clean_title = remove_leading_number(item.title)
        anchor = slugify(clean_title)
        toc_md += f"\n{index}. [{clean_title}](#{anchor})"
        if item.subtopics:
            for sub_index, sub_item in enumerate(item.subtopics, start=1):
                sub_clean_title = remove_leading_number(sub_item.title)
                sub_anchor = slugify(sub_clean_title)
                toc_md += f"\n {index}.{sub_index}. [{sub_clean_title}](#{sub_anchor})"

    # toc_md += "\n\n##"
    return toc_md


def add_heading_ids(content: str):
    """Adds <h1>-<h6> tags with IDs, stripping serial numbers and markdown styling like ** or *."""
    lines = content.split("\n")
    updated_lines = []

    for line in lines:
        match = re.match(r"^(#{1,6}) (\d+\.\s+)?(.+)", line)
        if match:
            hashes, _, title = match.groups()
            level = len(hashes)
            clean_title = title.strip()            
            # Remove bold/italic markdown markers (e.g., **text**, *text*, __text__)
            clean_title_no_md = re.sub(r"[*_]{1,2}(.*?)[*_]{1,2}", r"\1", clean_title)

            anchor = slugify(clean_title_no_md)           
            updated_lines.append(f'<h{level} id="{anchor}"><strong>{clean_title_no_md}</strong></h{level}>')
        else:
            updated_lines.append(line)

    return "\n".join(updated_lines)


def refresh_ghl_token(ghl_integration: GHLIntegration) -> bool:
    """Refresh the GHL access token if expired."""

    token_url = "https://services.leadconnectorhq.com/oauth/token"
    headers = {
    "Content-Type": "application/x-www-form-urlencoded",
    "Accept": "application/json"
    }
    payload = {
        "client_id": settings.GHL_CLIENT_ID,
        "client_secret": settings.GHL_CLIENT_SECRET,
        "grant_type": "refresh_token",
        "refresh_token": ghl_integration.refresh_token,
    }
    response = requests.post(token_url, data=payload, headers=headers)

    if response.status_code == 200:
        token_data = response.json()
        ghl_integration.access_token = token_data["access_token"]
        ghl_integration.refresh_token = token_data["refresh_token"]
        ghl_integration.save()
        return True
    return False


def get_ghl_author_id(ghl_integration: GHLIntegration) -> Optional[str]:
    """Fetch the first author ID for the given location."""

    headers = {
    "Version": "2021-07-28",
    "Accept": "application/json",
    "Authorization" : f"Bearer {ghl_integration.access_token}"
    }

    authors_url = "https://services.leadconnectorhq.com/blogs/authors"
    params = {
        "locationId": ghl_integration.location_id,
        "limit": "5",
        "offset": "0",
    }

    try:
        response = requests.get(authors_url, headers=headers, params=params)
        if response.status_code == 200:
            authors = response.json().get("authors", [])
            return authors[0]["_id"] if authors else None
        elif response.status_code == 401:
            if refresh_ghl_token(ghl_integration):
                headers["Authorization"] = f"Bearer {ghl_integration.access_token}"
                response = requests.get(authors_url, headers=headers, params=params)
                if response.status_code == 200:
                    authors = response.json().get("authors", [])
                    return authors[0]["_id"] if authors else None
            return None
        else:
            logger.error(f"Failed to fetch authors: {response.text}")
            return None
    except Exception as e:
        logger.critical(f"Error fetching author ID: {str(e)}")
        return None


def sanitize_url_slug(slug: str):
    """
    Used to sanitize url slug before creating article on GHL.
    :param slug: URL slug to sanitize
    """
    slug = slug.lower().strip()
    slug = re.sub(r"[^a-z0-9-]+", "-", slug)
    slug = slug.strip("-")
    return slug


def sanitize_url(url: str) -> str | None:
    """
    Sanitize URL by removing/replacing invalid characters and validating format.
    Returns None if URL cannot be sanitized to a valid format.

    :param url: URL string to sanitize
    :return: Sanitized URL string or None if invalid
    """
    if not url or not isinstance(url, str):
        return None

    url = url.strip()
    if not url:
        return None

    try:
        # Replace spaces with %20 (URL encoding for spaces)
        url = url.replace(' ', '%20')

        # Parse the URL to validate its structure
        parsed = urlparse(url)

        # Check if it has a valid scheme and netloc
        if not parsed.scheme or not parsed.netloc:
            return None

        # Only allow http and https schemes
        if parsed.scheme not in ['http', 'https']:
            return None

        # Reconstruct the URL to ensure it's properly formatted
        sanitized_url = parsed.geturl()

        # Additional validation: check if the domain part looks reasonable
        if ' ' in parsed.netloc or len(parsed.netloc) < 3:
            return None

        return sanitized_url

    except Exception:
        # If any error occurs during parsing/sanitization, return None
        return None


def calculate_read_time_and_word_count(content: str) -> tuple[float, int]:
    "return calculate read time and word count for payload"
    soup = BeautifulSoup(content, "html.parser")
    text = soup.get_text()
    words = len(text.split())
    read_time = words / 200
    return round(read_time, 3), words

def get_image_url(file_id: str, ghl_integration: GHLIntegration) -> str | None:
    """
    Fetches the image URL from LeadConnector API given a file_id.

    Args:
        file_id (str): The ID of the image file to look for.

    Returns:
        str | None: The image URL if found, otherwise None.
    """
    url = "https://services.leadconnectorhq.com/medias/files"

    querystring = {
        "sortBy": "createdAt",
        "sortOrder": "asc",
        "altType": "location"
    }

    headers = {
        "Authorization": f"Bearer {ghl_integration.access_token}",
        "Version": "2021-07-28",
        "Accept": "application/json"
    }

    response = requests.get(url, headers=headers, params=querystring)
    if response.status_code in [200, 201]:
        data = response.json()
        for item in data.get("files", []):
            if item.get("_id") == file_id:
                return item.get("url")
        return None
    else:
        logger.error(f"Error fetching files: {response.status_code}, {response.text}")
        return None


def upload_image_to_ghl(image_url: str, ghl_integration: GHLIntegration):
    '''
    uploading the image to ghl media library
    '''

    if not image_url:
        return {"status": "Failed", "error": "Image url is required for GHL blog post"}

    upload_url = "https://services.leadconnectorhq.com/medias/upload-file"

    headers = {
        "Authorization": f"Bearer {ghl_integration.access_token}",
        "Version": "2021-07-28",
    }

    image_response = requests.get(image_url)
    image_bytes = image_response.content


    files = {
        'file': ('image.png', image_bytes, 'image/png'),
    }

    try:
        response = requests.post(upload_url, headers=headers, files=files)
        if response.status_code in [200, 201] :
            logger.info(f"image {response.json()}")
            file_id = response.json().get("fileId", "")
            upload_image_url = get_image_url(file_id, ghl_integration)
            return upload_image_url
        elif response.status_code == 401:
            if refresh_ghl_token(ghl_integration):
                headers["Authorization"] = f"Bearer {ghl_integration.access_token}"
                response = requests.post(upload_url, headers=headers, files=files)
                if response.status_code in [200, 201]:
                    file_id = response.json().get("fileId", "")
                    upload_image_url = get_image_url(file_id, ghl_integration)
                    return upload_image_url
            return None
        else:
            logger.error(f"Failed to Upload image: {response.text}")
            return None
    except Exception as e:
        logger.critical(f"Error Uploading image: {str(e)}")
        return None



def post_article_to_ghl(
    article: Article,
    selected_categories: str,
    ghl_integration: GHLIntegration,
    title: str,
    content: str,
    image_url: str,
    status: Literal["DRAFT", "PUBLISHED"],
    blog_id: str,
    author_id: str,
    update_published_article: bool = False
) -> Dict:
    """Post the article to GHL with all required fields."""

    headers = {
    "Version": "2021-07-28",
    "Accept": "application/json, text/plain, */*",
    "Authorization" : f"Bearer {ghl_integration.access_token}",
    "Content-Type" : "application/json",
    }

    urlSlug = article.url_slug if article.url_slug else sanitize_url_slug(title.lower().replace(" ", "-")[:50])
    canonicalLink = f"{ghl_integration.ghl_domain}/post/{urlSlug}"
    selected_categories = [] if selected_categories == 1 else [selected_categories]

    data = {
        "title": title,
        "locationId": ghl_integration.location_id,
        "blogId": blog_id,
        "imageUrl": image_url,
        "description": title[:150],
        "rawHTML": content,
        "status": status,
        "imageAltText": f"Featured image for {title}",
        "categories": selected_categories,
        "author": author_id,
        "urlSlug": urlSlug,
        "publishedAt": datetime.datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ'),
        "tags": ["blog", "seo"],
        "canonicalLink": canonicalLink,
    }

    if update_published_article:
        read_time, word_count = calculate_read_time_and_word_count(content)
        data["archived"] = False
        data["type"] = "manual"
        data["readTimeInMinutes"] =  read_time
        data["wordCount"] = word_count
        post_id = get_blog_post_id(canonicalLink, ghl_integration)

        if not post_id:
            logger.error("Article ID not found for update")
            return {"status": "error", "err_id": "ARTICLE_ID_NOT_FOUND", "error_message": "Article ID not found for update"}

        blog_post_url = f"https://services.leadconnectorhq.com/blogs/posts/{post_id}"
        http_method = "PUT"

    else:
        blog_post_url = "https://services.leadconnectorhq.com/blogs/posts"
        http_method = "POST"

    response = requests.request(
        method=http_method,
        url=blog_post_url,
        headers=headers,
        json=data
    )

    if response.status_code in [200, 201]:
        response_data = response.json()
        article.article_link = canonicalLink
        article.is_posted = True
        article.posted_to = "ghl"
        article.article_status = status == "PUBLISHED" and "publish" or "draft"
        article.posted_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
        article.save()

        return {"status": "success", "article_link": canonicalLink}

    elif response.status_code == 401:
        if refresh_ghl_token(ghl_integration):
            headers["Authorization"] = f"Bearer {ghl_integration.access_token}"
            response = requests.request(
                    method=http_method,
                    url=blog_post_url,
                    headers=headers,
                    json=data
                )

            if response.status_code in [200, 201]:
                response_data = response.json()
                article.article_link = canonicalLink
                article.is_posted = True
                article.posted_to = "ghl"
                article.article_status = status == "PUBLISHED" and "publish" or "draft"
                article.posted_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
                article.save()

                return {"status": "success", "article_link": canonicalLink}

        return {"status": "Failed", "error": "Token refresh failed"}

    else:
        return {"status": "Failed", "error": response.text}


def publish_article_to_ghl(
    article: Article,
    status: Literal["draft", "publish"] = "publish",
    site_id: str | None = None,
    selected_categories: str = None,
    update_published_article: bool = False
) -> Dict:
    """Main function to orchestrate publishing an article to GHL."""

    # Fetch GHL integration
    if not update_published_article:
        ghl_integration: GHLIntegration = (
            article.website.ghlintegration_set.filter(site_id=site_id).first()
            or article.website.ghlintegration_set.first()
        )
    else:
        all_integrations: QuerySet[GHLIntegration] = article.website.ghlintegration_set.filter(ghl_domain__isnull=False)

        ghl_integration = None
        article_domain = urlparse(article.article_link).netloc.replace("www.", "")

        for integration in all_integrations:
            raw_site_url = integration.ghl_domain
            if not raw_site_url.startswith("http"):
                raw_site_url = "https://" + raw_site_url

            integration_domain = urlparse(raw_site_url).netloc.replace("www.", "")

            if article_domain == integration_domain:
                ghl_integration = integration
                break

    if not ghl_integration:
        return {"status": "Failed", "error": "No GHL integration found for this website"}

    image_url = article.selected_featured_image.image_url if article.selected_featured_image else None
    image_url = upload_image_to_ghl(image_url, ghl_integration)
    if not image_url:
        return {"status": "Failed", "error": "Image URL is required for GHL blog post"}

    author_id = get_ghl_author_id(ghl_integration)
    if not author_id:
        return {"status": "Failed", "error": "Failed to fetch author ID"}

    article_title: str = article.title
    article_content: str = article.content
    html_content: str = markdown.markdown(article_content, extensions=["tables"])

    soup = BeautifulSoup(html_content, "html.parser")
    for table in soup.find_all("table"):
        table["style"] = "border-collapse: collapse; width: 100%; margin: 20px 0;"
        for th in table.find_all("th"):
            th["style"] = "border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2; text-align: left;"
        for td in table.find_all("td"):
            td["style"] = "border: 1px solid #ddd; padding: 8px;"

    for ol in soup.find_all("ol"):
        for li in ol.find_all("li"):
            for p in li.find_all("p"):
                p["style"] = "display: inline;"

    html_content = str(soup)
    ghl_status = "PUBLISHED" if status == "publish" else "DRAFT"

    return post_article_to_ghl(
        article=article,
        selected_categories=selected_categories,
        ghl_integration=ghl_integration,
        title=article_title,
        content=html_content,
        image_url=image_url,
        status=ghl_status,
        blog_id=ghl_integration.site_id,
        author_id=author_id,
        update_published_article=update_published_article
    )


def get_blog_post_id(link: str, ghl_integration: GHLIntegration):
    """
    Fetches the blog post ID that matches the given URL slug.

    :param url_slug: The slug of the blog post to find.
    :return: The matching blog post ID or None if not found.
    """

    url = "https://services.leadconnectorhq.com/blogs/posts/all"

    query_params = {
        "locationId": ghl_integration.location_id,
        "blogId": ghl_integration.site_id,
        "limit": "10",
        "offset": "0",
        "status": "PUBLISHED"
    }

    headers = {
        "Authorization": f"Bearer {ghl_integration.access_token}",
        "Version": "2021-07-28",
        "Accept": "application/json"
    }
    parsed_url = urlparse(link)
    url_slug = parsed_url.path.split("/")[-1]
    try:
        response = requests.get(url, headers=headers, params=query_params)
        response_data = response.json()

        if response.status_code == 200 and "blogs" in response_data:
            matching_blog = next(
                (blog for blog in response_data["blogs"] if blog["urlSlug"] == url_slug),
                None
            )

            if matching_blog:
                return matching_blog["_id"]
            else:
                return None
        else:
            logger.error(f"Error: {response_data}")
            return None

    except Exception as e:
        logger.critical(f"Request failed: {str(e)}")
        return None


def get_redis_connection(db: int = 0):
    """
    A context manager for Redis connection.

    :param db: Redis database number (default is 0)
    """
    from contextlib import contextmanager

    @contextmanager
    def redis_connection_manager(db):
        if os.environ.get('REDIS_SENTINELS_PORTS'):
            # Define Sentinel nodes
            sentinel_nodes = os.environ['REDIS_SENTINELS_PORTS'].split(',')
            sentinels = [
                (os.environ['REDIS_HOST'], int(sentinel_nodes[0])),
                (os.environ['REDIS_HOST'], int(sentinel_nodes[1])),
                (os.environ['REDIS_HOST'], int(sentinel_nodes[2])),
            ]

            # Set up Redis connection
            sentinel = redis.Sentinel(sentinels, socket_timeout=60, password=os.environ['REDIS_PASSWORD'])
            connection = sentinel.master_for('abun-redis-master', password=os.environ['REDIS_PASSWORD'], db=db)

        else:
            # Set up Redis connection
            connection = redis.Redis(
                host=os.environ['REDIS_HOST'],
                port=os.environ['REDIS_PORT'],
                db=db,
                password=os.environ.get('REDIS_PASSWORD')
            )

        try:
            yield connection
        finally:
            connection.close()

    return redis_connection_manager(db)


def get_relative_time(timestamp: datetime.datetime):
    """
    Converts a timestamp to a relative time string:
    :param timestamp: Timestamp to convert:
    """
    now = datetime.datetime.now(tz=ZoneInfo('UTC'))
    if timestamp.tzinfo is None:
        timestamp = timestamp.replace(tzinfo=ZoneInfo('UTC'))

    diff = now - timestamp
    seconds = diff.total_seconds()

    if seconds < 60:
        return "just now"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
    elif seconds < 86400:
        hours = int(seconds // 3600)
        return f"{hours} hour{'s' if hours != 1 else ''} ago"
    elif seconds < 2592000:  # 30 days
        days = int(seconds // 86400)
        return f"{days} day{'s' if days != 1 else ''} ago"
    else:
        return timestamp.strftime("%d %b %Y")


def get_next_auto_scan_run_time(user: User):
    """
    Calculate the next run time for the auto-scan cronjob.
    The cronjob runs every 24 hours at 9:00 AM user's timezone.
    :param user: User model instance.
    :return: Formatted string showing next run time
    """
    # Get current time in user's timezone
    user_tz = ZoneInfo(user.user_timezone or 'UTC')
    now = datetime.datetime.now(tz=user_tz)

    # Set target time to 9:00 AM
    target_time = now.replace(hour=9, minute=0, second=0, microsecond=0)

    # If current time is past 9:00 AM, schedule for tomorrow
    if now >= target_time:
        target_time += datetime.timedelta(days=1)
    
    # Format the output
    if target_time.date() == now.date():
        return f"Today, {target_time.strftime('%I:%M %p')}"
    elif target_time.date() == (now + datetime.timedelta(days=1)).date():
        return f"Tomorrow, {target_time.strftime('%I:%M %p')}"
    else:
        return target_time.strftime('%B %d, %I:%M %p')


def glossary_fetch_internal_links(website_id: int, phrase: str, body_content) -> Dict[str, Any]:
    """
    Given a phrase and the glossary content body,
    finds the best internal links using ChromaDB similarity search.
    Returns a dictionary: {'success': True, 'internal_links': List[Dict]}
    """
    try:
        if isinstance(body_content, dict):
            body_content = body_content.get("content", "")
            if not isinstance(body_content, str):
                body_content = str(body_content)

        # Extract sentence containing the phrase. Default to the phrase itself
        search_text = phrase

        # Escape special regex characters in phrase
        escaped_phrase = re.escape(phrase)

        # Pattern to match a sentence containing the phrase
        pattern = f'[^.!?]*{escaped_phrase}[^.!?]*[.!?]'
        match = re.search(pattern, body_content, re.IGNORECASE)

        # If a sentence is found, use it as the search text
        if match:
            search_text = match.group(0).strip()
            logger.info(f"Found sentence: '{search_text}'")
        else:
            logger.info(f"No matching sentence found for '{phrase}' phrase.")

        # Create Chroma DB manager
        chromaDBManager = ChromaDBManager()

        # First, try to find similar pages with the full sentence
        similar_pages = chromaDBManager.find_similar_pages(search_text, website_id)
        logger.info(f"Found {len(similar_pages)} similar pages using sentence")

        # If no similar pages found, fallback to original phrase
        if not similar_pages and search_text != phrase:
            logger.info(f"No similar pages found with sentence. Trying with original phrase: '{phrase}'")
            similar_pages = chromaDBManager.find_similar_pages(phrase, website_id)

            if similar_pages:
                logger.info(f"Found {len(similar_pages)} similar pages using original phrase")

        # If still no similar pages found, return empty list
        if not similar_pages:
            return {"success": True, "internal_links": []}

        # Sort similar pages by similarity score descending
        similar_pages.sort(key=lambda page: page["similarity"], reverse=True)

        # Format the result into a list of internal link dicts
        internal_links = []
        for page in similar_pages:
            internal_links.append({
                "url": page["url"],
                "title": page["title"],
                "summary": page.get("summary", ""),
                "similarity": page["similarity"],
                "anchor_text": phrase  # Add the phrase as the anchor text
            })

        return {"success": True, "internal_links": internal_links}

    except Exception as e:
        logger.error(f"Error in fetch_internal_links: {str(e)}", exc_info=True)
        return {"success": False, "internal_links": [], "error": str(e)}


def glossary_add_internal_links(body: str, links_data: list, term: str) -> str:
    '''
    Adding internallink to glossary content
    '''
    def find_existing_links(text):
        return [(m.start(), m.end()) for m in re.finditer(r'(\[.*?\]\(.*?\))|(<a\s+href=".*?">.*?</a>)', text, re.IGNORECASE)]

    def is_inside_existing_link(pos, existing_links):
        for start, end in existing_links:
            if start <= pos < end:
                return True
        return False

    for link in links_data:
        anchor = link['anchor_text']
        url = link['url']

        pattern = re.escape(anchor)
        matches = list(re.finditer(pattern, body))

        if not matches:
            continue

        existing_links = find_existing_links(body)

        for match in matches:
            start, end = match.start(), match.end()

            if not is_inside_existing_link(start, existing_links):
                linked_text = f'<a href="{url}">{match.group(0)}</a>'
                body = body[:start] + linked_text + body[end:]
                break

    return body

def remove_repeated_line(body, term):
    '''
    removing repeated line of terms
    '''
    lines = body.splitlines()
    if lines:
        first_line = lines[0].strip()
        cleaned_first_line = re.sub(r'^[#\s]*\**(.*?)\**$', r'\1', first_line).strip()
        if cleaned_first_line.lower() == term.lower():
            lines = lines[1:]
            body = "\n".join(lines)
    return body


def fetch_published_wp_posts(wp_integration, max_posts=40):
    """
    Fetches published posts from a WordPress site using the REST API.

    Args:
        wp_integration (WPIntegration): An instance containing WordPress credentials and site URL.
        max_posts (int): The maximum number of posts to fetch. Defaults to 40.

    Returns:
        list: A list of published post dictionaries fetched from the WordPress site.

    Raises:
        Exception: If the HTTP request fails or the response cannot be parsed.
    """
    all_posts = []
    page = 1
    per_page = 20

    while len(all_posts) < max_posts:
        try:
            response = requests.get(
                f"{wp_integration.site_url}/wp-json/wp/v2/posts",
                params={
                    "status": "publish",
                    "per_page": per_page,
                    "page": page
                },
                auth=HTTPBasicAuth(wp_integration.user_login, wp_integration.password),
                timeout=10
            )
            response.raise_for_status()
            posts = response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch posts from WordPress: {e}")
            raise Exception(f"Error fetching WordPress posts: {e}")
        except ValueError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            raise Exception(f"Invalid JSON response: {e}")

        if not posts:
            break

        all_posts.extend(posts)

        if len(posts) < per_page:
            break

        page += 1

    return all_posts


def create_article_from_wp_post(post: Dict, website: Website):
    """
    Creates an Article instance from a WordPress post.
    :param post: WordPress post data
    :param website: Website instance
    """
    try:
        html_converter = html2text.HTML2Text()
        html_converter.ignore_links = False
        html_converter.body_width = 0
        html_converter.parser = "html5lib"

        logger.debug(f"Post data: {post}")
        content_data = post.get("content", {})        
        content = content_data.get("rendered", "") if isinstance(content_data, dict) else ""

        if not content or not is_valid_html(content):
            logger.error(f"Post ID {post.get('id')} has invalid or empty HTML content")
            return None

        soup = BeautifulSoup(content, "html5lib")
        all_links = soup.find_all('a', href=True)
        all_imgs = soup.find_all('img', src=True)

        # Clean unwanted tags
        for tag in soup(["script", "style", "iframe", "noscript", "form", "input"]):
            tag.decompose()

        for tag in soup.find_all(True):
            tag.attrs.pop("style", None)

        sanitized_html = soup.body.decode_contents() if soup.body else str(soup)        
        internal_link_count, external_link_count = get_internal_external_link_count(all_links, all_imgs, website)    
        try:
            content_markdown = html_converter.handle(sanitized_html).strip()
        except Exception as e:
            content_markdown = sanitized_html

        content_markdown = html.unescape(content_markdown)
        word_count = get_word_count(content)

        post_date = parse_datetime(post.get("date")) if post.get("date") else timezone.now()

        article = Article.objects.create(
            website=website,
            article_uid=f"wp-{post.get('id')}-{uuid.uuid4()}",
            title=post.get("title", {}).get("rendered", "Untitled"),
            url=post.get("link"),
            content=content_markdown,
            word_count=word_count,
            is_generated=True,
            posted_to="wordpress",
            article_link=post.get("link"),
            is_posted=True,
            posted_on=post_date,
            internal_link_count= internal_link_count,
            external_link_count= external_link_count,            
        )

        return article

    except Exception as e:
        logger.critical("Failed to create article from WP post ID %s: %s",
                        post.get("id"), str(e), exc_info=True)
        return None


def fetch_and_create_featured_image(post, wp_integration):
    """
    Args:
        post (dict): The WordPress post data fetched from the WP REST API.
        wp_integration: wordpress integration object

    Fetch media for the post and create feature image object.
    """
    media_id = post.get("featured_media")
    if not media_id:
        return None

    try:
        response = requests.get(
            f"{wp_integration.site_url}/wp-json/wp/v2/media/{media_id}",
            auth=HTTPBasicAuth(wp_integration.user_login, wp_integration.password),
            timeout=10
        )
    except requests.RequestException as e:
        logger.error(
            "Request to fetch media %s failed for site %s: %s",
            media_id,
            wp_integration.site_url,
            str(e),
            exc_info=True
        )
        return None

    if response.status_code != 200:
        logger.error(
            "Failed to fetch media %s from %s: HTTP %s",
            media_id,
            wp_integration.site_url,
            response.status_code
        )
        return None

    try:
        media = response.json()
    except ValueError as e:
        logger.error("Invalid JSON response for media %s: %s", media_id, str(e), exc_info=True)
        return None

    image_url = media.get("source_url")
    if not image_url:
        logger.error("Media %s has no source_url field", media_id)
        return None

    try:
        featured_image = FeaturedImage.objects.create(
            image_url=image_url,
            source='uploaded_image',
            template_id_used="",
            template_image_url=""
        )
    except Exception as e:
        logger.critical("Failed to create FeaturedImage for URL %s: %s", image_url, str(e), exc_info=True)
        return None

    return featured_image


def save_wordpress_published_article(article: Article,
                                     post: Dict,
                                     featured_image: FeaturedImage | None,
                                     website: Website,
                                     gsc_position: float | None):
    """
    save wordpress published article
    :param article: Article instance
    :param post: post data
    :param featured_image: FeaturedImage instance
    :param website: Website instance
    :param gsc_position: gsc position
    """
    post_date = parse_datetime(post.get("date")) if post.get("date") else timezone.now()

    try:
        # Create the WordpressPublishedArticle object
        wp_article = WordpressPublishedArticle.objects.create(
            website=website,
            article=article,
            post_id=post.get("id"),
            media_id=post.get("featured_media"),
            title=post.get("title", {}).get("rendered", ""),
            slug=post.get("slug", ""),
            url=post.get("link", ""),
            published_date=post_date,
            gsc_position=round(gsc_position, 2) if gsc_position is not None else None
        )

    except Exception as e:
        logger.critical(
            "Failed to create WordpressPublishedArticle for post ID %s: %s",
            post.get("id"), str(e), exc_info=True
        )
        return None

    # Attach featured image to the article
    if featured_image:
        try:
            article.selected_featured_image = featured_image
            article.featured_images.add(featured_image)
            article.save()

        except Exception as e:
            logger.critical(
                "Failed to associate featured image with article UID %s: %s",
                article.article_uid, str(e), exc_info=True
            )

    return wp_article


def is_valid_html(html_string):
    '''
    Check the html is valid
    '''
    try:
        soup = BeautifulSoup(html_string, "html5lib")
        return bool(soup.body and soup.body.get_text(strip=True))
    except Exception as err:
        logger.critical(f"Html is invalid {err}")
        return False


def is_quota_limit_exceeds() -> bool:
    """
    Returns `True` if the quota limit is reached, False otherwise.
    """
    with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
        try:
            total_token_used = redis_connection.get('token_used')

            if total_token_used is None:
                return False

        except TypeError:
            return False

        return int(total_token_used) >= 11_000_000


def add_or_update_gpt_token_usage(token_used: int):
    """
    Add or update the GPT token usage in Redis.
    """
    with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
        try:
            total_token_used = redis_connection.get('token_used')
            if total_token_used:
                # Update the token usage
                redis_connection.set('token_used', int(total_token_used) + token_used)

            else:
                # Calculate the time remaining until next 00:00 IST
                now = timezone.now()

                # Convert current time to IST for comparison
                now_ist = now.astimezone(timezone(datetime.timedelta(hours=5, minutes=30)))

                # Get today's midnight in IST
                today_midnight_ist = now_ist.replace(hour=0, minute=0, second=0, microsecond=0)

                # If current time is past today's midnight, calculate for tomorrow's midnight
                if now_ist >= today_midnight_ist:
                    next_reset_ist = today_midnight_ist + datetime.timedelta(days=1)
                else:
                    next_reset_ist = today_midnight_ist

                # Convert back to the same timezone as 'now' for accurate calculation
                next_reset_utc = next_reset_ist.astimezone(now.tzinfo)

                # Calculate the time to live (TTL) in seconds
                ttl = int((next_reset_utc - now).total_seconds())

                # Ensure TTL is positive (safety check)
                if ttl <= 0:
                    logger.error("TTL calculation resulted in non-positive value. Defaulting to 24 hours.")
                    ttl = 24 * 60 * 60  # Default to 24 hours if calculation fails

                # Set the token usage with TTL
                redis_connection.set('token_used', token_used, ex=ttl)

        except TypeError:
            redis_connection.set('token_used', token_used)


def get_gpt_model_name() -> str:
    """
    Returns the GPT model.
    """
    if is_quota_limit_exceeds():
        return "gpt-4o-mini"
    else:
        return "gpt-4.1-mini"


def add_calculator_branding(html_code: str, calculator_name: str = "") -> str:
    """
    Adds 'Made with Abun.com' branding to calculator HTML code.

    :param html_code: The original HTML code
    :param calculator_name: The name of the calculator for the tracking URL
    :return: HTML code with branding added
    """
    branding_html = f'''
    <style>
        .abun-branding-container {{
            display: flex;
            justify-content: center;
            margin-top: 20px;
            padding: 10px;
        }}
        .abun-branding-button {{
            font-family: "Inter", Sans-serif;
            font-size: 14px;
            display: flex;
            align-items: center;
            border-style: solid;
            border-width: 1px 1px 1px 1px;
            border-color: #D2D2EB;
            border-radius: 200px;
            padding: 15px;
            transition: all .09s ease !important;
            box-shadow: 0px 3px 3px 0px rgba(19, 48, 66, 0.07058823529411765);
            text-decoration: none;
            color: inherit;
        }}
        .abun-branding-button:hover {{
            transform: scale(1.02);
            cursor: auto !important;
            box-shadow: -2px 5px 3px 0px rgba(19, 48, 66, 0.07058823529411765);
        }}
        .abun-branding-button:hover .abun-branding-link {{
            color: #2942ff !important;
        }}
        .abun-branding-link {{
            text-decoration: underline;
            color: #000000;
            margin-left: 5px;
        }}
    </style>
    <div class="abun-branding-container">
        <div class="abun-branding-button">
            <img style="width: 18px; margin-right: 8px" src="https://abun.com/wp-content/uploads/2025/06/Ai-icon.svg" alt="AI Icon">
            Made with <a href="https://abun.com?via={calculator_name}" target="_blank" class="abun-branding-link">Abun.com</a>
        </div>
    </div>
    '''

    # Try to insert before closing body tag, if not found, append at the end
    if '</body>' in html_code.lower():
        html_code = html_code.replace('</body>', f'{branding_html}</body>')
    elif '</div>' in html_code:
        # Find the last closing div and insert before it
        last_div_index = html_code.rfind('</div>')
        if last_div_index != -1:
            html_code = html_code[:last_div_index] + branding_html + html_code[last_div_index:]
        else:
            html_code += branding_html
    else:
        html_code += branding_html

    return html_code


def remove_calculator_branding(html_code: str) -> str:
    """
    Removes 'Made with Abun.com' branding from calculator HTML code.

    :param html_code: The HTML code with branding
    :return: HTML code with branding removed
    """
    # Remove the branding style block
    style_pattern = r'<style>.*?\.abun-branding-container.*?</style>'
    html_code = re.sub(style_pattern, '', html_code, flags=re.DOTALL | re.IGNORECASE)

    # Remove the branding container div
    branding_container_pattern = r'<div class="abun-branding-container">.*?</div>\s*</div>'
    html_code = re.sub(branding_container_pattern, '', html_code, flags=re.DOTALL | re.IGNORECASE)

    # Also remove any standalone branding paragraphs
    branding_p_pattern = r'<p[^>]*>.*?Made with.*?Abun\.com.*?</p>'
    html_code = re.sub(branding_p_pattern, '', html_code, flags=re.DOTALL | re.IGNORECASE)

    return html_code.strip()


def get_meta_description(url):
    '''
    Extracting description from the meta tag for the connected domains.
    '''
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"
        }
        response = requests.get(f'https://{url}', headers=headers, timeout=10)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        for attr_name, attr_value in [
            ('name', 'description'),
            ('property', 'og:description'),
            ('name', 'og:description'),
            ('name', 'twitter:description')
        ]:
            tag = soup.find('meta', attrs={attr_name: attr_value})
            if tag and tag.get('content'):
                return tag['content'].strip()

        return ''
    
    except (requests.exceptions.ConnectTimeout, requests.exceptions.ReadTimeout):
        logger.error(f"Timeout occurred while fetching metadata for {url}")
        return ''
    except requests.exceptions.RequestException as e:
        logger.error(f"Request error while fetching metadata for {url}: {e}")
        return ''
    except Exception as e:
        logger.critical(f"Unexpected error while fetching metadata for {url}: {e}")
        return ''

def classify_link(url, base_domain, skip_keywords=None):
    '''
    Validating the link and returning the type of Links
    '''
    
    if not url:
        return None

    url = url.strip()
    
    if skip_keywords:
        for keyword in skip_keywords:
            if keyword in url or (keyword == '#' and url.startswith('#')):
                logger.info(f"Skipping link due to keyword match ({keyword}): {url}")
                return None

    parsed = urlparse(url)
    if parsed.scheme not in ["http", "https"] or not parsed.netloc:
        logger.info(f"Skipping non-http(s) or empty netloc link: {url}")
        return None

    domain = parsed.netloc.replace("www.", "").lower()
    path = parsed.path.strip("/")
    if not path:
        logger.info(f"Skipping root domain link: {url}")
        return None
    
    return "internal" if domain == base_domain else "external"

def get_internal_external_link_count(all_links, all_imgs, website):
    '''
    Get internal/external Link count
    '''
    internal_link_count = 0
    external_link_count = 0

    try:
        base_domain = website.domain.replace("www.", "").lower()
        skip_keywords = ['unsplash', '#']

        # Process <a href="">
        for tag in all_links:
            href = tag.get('href', '')
            result = classify_link(href, base_domain, skip_keywords)
            if result == "internal":
                internal_link_count += 1                
            elif result == "external":
                external_link_count += 1                

        # Process <img src="">
        for tag in all_imgs:
            src = tag.get('src', '')
            result = classify_link(src, base_domain, skip_keywords=['unsplash'])
            if result == "internal":
                internal_link_count += 1                
            elif result == "external":
                external_link_count += 1                

    except Exception as e:
        logger.critical(f"Error while counting links: {e}")    
    return internal_link_count, external_link_count


def fetch_gsc_insights(user: User, credentials, params, site_url):
    func_to_exec = 'fetch_search_performance_analytics_data'
    try:
        callable_func: Callable = getattr(google_integration_utils, func_to_exec)
        params.update({
            'credentials': credentials,
            'website_url': site_url
        })

        data = callable_func(**params)

        # Adding keyword status data to rows
        if 'rows' in data:
            for row in data['rows']:
                keyword = row['keys'][0]

                # Fetch the GSCKeywordStatus for this keyword
                gsc_keyword_status = get_gsc_keyword_status_by_keyword(user, keyword)

                # If the GSCKeywordStatus exists, add the data to the response
                if gsc_keyword_status:
                    row['generated'] = gsc_keyword_status.generated
                    row['processing'] = gsc_keyword_status.processing
                    row['failed'] = gsc_keyword_status.failed
                    row['project_id'] = gsc_keyword_status.project_id
                    row['keyword_hash'] = gsc_keyword_status.keyword_hash
                else:
                    # If keyword status doesn't exist, mark as not available
                    row['generated'] = None
                    row['processing'] = None
                    row['failed'] = None
                    row['project_id'] = None
                    row['keyword_hash'] = None

        return {"data": data}
    
    except TypeError as err:
        return []

    except HttpError as err:
        logger.error(err)


def fetch_gsc_insights(user: User, credentials: Credentials, params: Dict, site_url: str):
    """
    Fetches the GSC insights from google APIs.
    :param user: User model instance
    :param credentials: Google oauth2 credentials
    :param params: Params to be passed to the google API
    :param site_url: Connected website URL
    :return: Dict
    """
    func_to_exec = 'fetch_search_performance_analytics_data'
    try:
        callable_func: Callable = getattr(google_integration_utils, func_to_exec)
        params.update({
            'credentials': credentials,
            'website_url': site_url
        })

        data = callable_func(**params)

        # Adding keyword status data to rows
        if 'rows' in data:
            for row in data['rows']:
                keyword = row['keys'][0]

                # Fetch the GSCKeywordStatus for this keyword
                gsc_keyword_status = get_gsc_keyword_status_by_keyword(user, keyword)

                # If the GSCKeywordStatus exists, add the data to the response
                if gsc_keyword_status:
                    row['generated'] = gsc_keyword_status.generated
                    row['processing'] = gsc_keyword_status.processing
                    row['failed'] = gsc_keyword_status.failed
                    row['project_id'] = gsc_keyword_status.project_id
                    row['keyword_hash'] = gsc_keyword_status.keyword_hash
                else:
                    # If keyword status doesn't exist, mark as not available
                    row['generated'] = None
                    row['processing'] = None
                    row['failed'] = None
                    row['project_id'] = None
                    row['keyword_hash'] = None

        return {"data": data}

    except TypeError as err:
        return []

    except HttpError as err:
        logger.error(err)

        try:
            message = err.reason
        except AttributeError:
            message = "Failed to fetch the data from google"

        return {'err_id': "GOOGLE_API_ERROR", 'message': message}

    except Exception as err:
        logger.critical(err)
        return {'err_id': "SERVER_ERROR", 'message': "Something went wrong"}


def get_or_add_gsc_insights_in_redis(user:User,
                                     credentials: Credentials,
                                     params: Dict,
                                     site_url: str,
                                     days: int,
                                     get_latest_data: bool):
    """
    Get/Add GSC insights in redis DB.
    :param user: User model instance
    :param credentials: Google oauth2 credentials
    :param params: Params to be passed to the google API
    :param site_url: Connected website URL
    :param days: Number of days for which the data is requested
    :param get_latest_data: If True, fetch the latest data from google APIs
    :return: Dict
    """
    with get_redis_connection(db=REDIS_GSC_INSIGHTS_DB) as redis_connection:
        redis_key = f"{site_url}:{days}:gsc:insights"
        meta_key = f"{redis_key}:last_updated"

        # Check last update timestamp
        last_updated_bytes = redis_connection.get(meta_key)
        now = timezone.now()

        if not get_latest_data and last_updated_bytes:
            last_updated_str = last_updated_bytes.decode()
            last_updated = timezone.datetime.fromisoformat(last_updated_str.replace('Z', '+00:00'))

            data = redis_connection.get(redis_key)
            if (now - last_updated).days < 7 and data:
                return {"data":json.loads(data), "last_updated": last_updated}

        # Fetch new GSC data (Replace this with your actual fetch logic)
        gsc_data = fetch_gsc_insights(user, credentials, params, site_url)

        if gsc_data.get("data"):
            # Store in Redis
            last_updated = now.strftime("%Y-%m-%dT%H:%M:%SZ")
            data = gsc_data.get("data")
            redis_connection.set(redis_key, json.dumps(data))
            redis_connection.expire(redis_key, REDIS_GSC_INSIGHTS_EXPIRY)
            redis_connection.set(meta_key, last_updated)
            redis_connection.expire(meta_key, REDIS_GSC_INSIGHTS_EXPIRY)

            last_updated = timezone.datetime.fromisoformat(last_updated.replace('Z', '+00:00'))

            return {"data":data, "last_updated": last_updated}

        else:
            return gsc_data
    
    
def fetch_additional_reddit_data(post_url: str, max_retries: int = 5, delay_sec: float = 1.5) -> dict:
    """
    Fetches additional data for a Reddit post.
    :param post_url: URL of the Reddit post
    :param max_retries: Maximum number of retries in case of failure
    :param delay_sec: Delay between retries
    :return: Dictionary containing additional post data
    """
    if not post_url.endswith(".json"):
        post_url = post_url.rstrip('/') + ".json"


    attempt = 0
    reddit = praw.Reddit(
        client_id=REDDIT_CLIENT_KEY,
        client_secret=REDDIT_SECRET_KEY,
        user_agent="AbunFinder/0.1 by abun1234"
    )

    while attempt < max_retries:
        try:
            submission = reddit.submission(url=post_url)

            original_title = html.unescape(submission.title.strip())
            cleaned_title = re.sub(r"\s*-\s*Reddit$", "", original_title, flags=re.IGNORECASE)
            cleaned_title = re.sub(r"\br/\S+", "", cleaned_title)
            cleaned_title = re.sub(r"\.\.\.", "", cleaned_title)
            cleaned_title = re.sub(r":", "", cleaned_title)
            cleaned_title = cleaned_title.strip()
            
            return {
                "subreddit_name": f"r/{submission.subreddit.display_name}" if submission.subreddit else "",
                "subreddit_id": submission.subreddit.id if submission.subreddit else "",
                "post_title": cleaned_title,
                "author": submission.author.name if submission.author else None,
                "score": submission.score,
                "upvote_ratio": submission.upvote_ratio,
                "num_comments": submission.num_comments,
                "selftext": submission.selftext,
                "subreddit_subscribers": submission.subreddit.subscribers if submission.subreddit else None,
                "created_utc": datetime.datetime.fromtimestamp(submission.created_utc, tz=datetime.timezone.utc) if submission.created_utc else None
            }

        except (praw.exceptions.APIException, prawcore.exceptions.RequestException) as retryable:
            attempt += 1
            logger.error(f"[Retry {attempt}/{max_retries}] Timeout/ConnectionError fetching {post_url}: {retryable}")
            time.sleep(delay_sec)

        except prawcore.exceptions.Forbidden as forbidden_error:
            # Handle 403 errors gracefully - these are common and expected
            logger.warning(f"[Reddit Access Denied] {post_url} | Error: {str(forbidden_error)} - Post may be private, deleted, or rate limited")
            break

        except Exception as e:
            logger.critical(f"[Non-retryable Reddit Fetch Error] {post_url} | Error: {str(e)}")
            break

    return {"error": f"Failed after {max_retries} retries"}


def create_ai_calculator_task(user: User,
                              website: Website,
                              ai_calculator: AICalculator,
                              modifications_instruction: str = None) -> Dict:
    """
    Creates an AI calculator generation or modification task to K8s or Fly.io based on DEBUG setting.
    :param user: User instance
    :param website: Website instance (required for generation)
    :param ai_calculator: AICalculator instance (required for modification)
    :param modifications_instruction: Instructions for modifying the calculator (required for modification)
    :return: Dictionary with status and task information
    """
    try:
        # Determine if this is generation or modification based on parameters
        if modifications_instruction:
            # This is a modification task
            is_generation = False

            # Check conversation history to confirm
            try:
                existing_conversation = json.loads(ai_calculator.conversation) if ai_calculator.conversation else []
            except (json.JSONDecodeError, TypeError):
                existing_conversation = []

            # If conversation is empty, treat as generation (shouldn't happen in modification flow)
            if len(existing_conversation) == 0:
                return {
                    'status': 'error',
                    'message': 'Cannot modify calculator with empty conversation history',
                    'calculator_id': ai_calculator.calculator_id
                }

            job_type = 'calculatormodification'
            task_data = {
                'calculator_id': ai_calculator.calculator_id,
                'modifications_instruction': modifications_instruction,
                'conversation_history': ai_calculator.conversation,
                'abun_webhook_url': reverse('wh-k8-ai-calculator'),
            }
            metadata = ai_calculator.calculator_id

        else:
            # This is a generation task
            is_generation = True
            job_type = 'calculatorgeneration'
            task_data = {
                'calculator_id': ai_calculator.calculator_id,
                'calc_type': ai_calculator.calc_type,
                'calc_description': ai_calculator.calc_description or '',
                'abun_webhook_url': reverse('wh-k8-ai-calculator'),
            }

            metadata = ai_calculator.calculator_id

        # Generate job ID
        job_id = generate_k8_job_id(job_type, username=user.username)

        # Store data in Redis
        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            redis_connection.set(job_id, json.dumps(task_data))
            redis_connection.expire(job_id, REDIS_ART_GEN_EXPIRY)

        # Create KubernetesJob entry
        k8_job = KubernetesJob(
            job_id=job_id,
            user=user,
            status='running',
            metadata=metadata,
        )
        k8_job.save()

        # Use unified script for both generation and modification
        script_name = 'ai_calculator_processor.py'
        task_type_label = 'modification' if not is_generation else 'generation'

        if DEBUG:
            # Use K8s for development - unified category for both generation and modification
            job_created = create_k8_job(
                job_id,
                'ai_calculator_generation',
                job_id,
                user.id,
                [job_id]
            )

            if not job_created:
                k8_job.status = 'failed'
                k8_job.save()
                return {
                    'status': 'error',
                    'message': f'Failed to create K8s job due to high load on the cluster.',
                    'job_id': job_id
                }

            return {
                'status': 'submitted',
                'message': f'Calculator {task_type_label} task submitted to K8s successfully.',
                'job_id': job_id,
                'calculator_id': metadata,
                'machine_id': 'NOT_REQUIRED'
            }

        else:
            # Use Fly.io for staging/production
            cmd = f"python3 {script_name} {job_id}"
            cmd = cmd.split()
            worker_props = {
                "config": {
                    "image": FLY_AI_CALCULATOR_IMAGE_URL,
                    "auto_destroy": True,
                    "init": {
                        "cmd": cmd
                    },
                    "restart": {
                        "policy": "on-failure",
                        "max_retries": K8_JOB_RETRIES
                    },
                    "guest": {
                        "cpu_kind": "shared",
                        "cpus": 1,
                        "memory_mb": 1024
                    }
                },
            }

            res = requests.post(
                f"{FLY_API_HOST}/apps/{FLY_AI_CALCULATOR_APP_NAME}/machines",
                headers={
                    'Authorization': f"Bearer {FLY_AI_CALCULATOR_DEPLOY_TOKEN}",
                    'Content-Type': 'application/json'
                },
                json=worker_props
            )

            if res.status_code != 200:
                k8_job.status = 'failed'
                k8_job.save()
                return {
                    'status': 'error',
                    'message': f'Failed to send task to Fly.io: {res.text}',
                    'job_id': job_id
                }

            # Get machine ID
            machine_id = res.json()['id']

            return {
                'status': 'submitted',
                'message': f'Calculator {task_type_label} task submitted to Fly.io successfully.',
                'job_id': job_id,
                'calculator_id': metadata,
                'machine_id': machine_id
            }

    except Exception as e:
        task_type_label = 'modification' if modifications_instruction else 'generation'
        return {
            'status': 'error',
            'message': f'Error submitting calculator {task_type_label} task: {str(e)}',
            'calculator_id': metadata if 'metadata' in locals() else 'unknown'
        }

def get_keywords_using_llm(icp, num_keywords):
    '''
    Batch function to generate unique keyword from Openai
    '''
    keywords = []
    batch_size = 200  # reasonable chunk size

    while True:
        remaining = int(num_keywords) - len(set(keywords))
        if remaining <= 0:
            break

        current_batch_size = min(batch_size, remaining)
        prompt = f"""
        My ICP is {icp}. What are some keywords they would search on Google?
        Give me exactly {current_batch_size} keywords.
        Do not number them. Just list one per line.
        Output ONLY the list of keywords — no numbering, no extra explanations.
        """

        client = OpenAI()
        try:
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[{"role": "user", "content": [{"type": "text", "text": prompt}]}],
                temperature=1,
                max_tokens=2048,
                top_p=1,
                frequency_penalty=0,
                presence_penalty=0
            )
            keywords_raw = response.choices[0].message.content
            logger.info(f"Generated batch of {current_batch_size} keywords. Raw length: {len(keywords_raw)}")
            if hasattr(response, 'usage'):
                logger.info(f"Token usage: prompt={response.usage.prompt_tokens}, completion={response.usage.completion_tokens}, total={response.usage.total_tokens}")

            batch_keywords = [kw.strip() for kw in keywords_raw.split("\n") if kw.strip()]
            keywords.extend(batch_keywords)
        except Exception as e:
            logger.critical(f"Error generating keywords: {e}")
            break

    # Deduplicate and trim to exactly num_keywords
    unique_keywords = list(dict.fromkeys(keywords))[:num_keywords]
    return unique_keywords


def get_youtube_metadata(url: str) -> dict:
    '''
    Get youtube video metadata like title and duration.
    '''
    ydl_opts = {
        'quiet': True,
        'skip_download': True,
    }

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        info_dict = ydl.extract_info(url, download=False)
        raw_title = info_dict.get("title", "Untitled")
        duration = info_dict.get("duration", 0)  # in seconds

    clean_title = re.sub(r'#\S+', '', raw_title)
    clean_title = re.sub(r'\s+', ' ', clean_title)

    return {
        "title": clean_title.strip(),
        "duration": duration  # in seconds
    }


def html_to_markdown_preserving_heading_ids(html: str) -> str:
    '''
    Maintaining the Id and toc href for On page links.
    :param html: html to markdown. 
    '''
    soup = BeautifulSoup(html, "html.parser")

    # Preserve heading tags with ID
    for heading in soup.find_all(["h1", "h2", "h3", "h4", "h5", "h6"]):
        heading_id = heading.get("id")
        if heading_id:
            heading.replace_with(str(heading))

    return md(str(soup), heading_style="ATX")


def normalize_toc_structure(html_content: str) -> str:
    '''
    Normalize toc nested li for wix
    :param html_content: html string
    '''
    soup = BeautifulSoup(html_content, "html.parser")
    try:
        for ol in soup.find_all("ol"):
            for li in ol.find_all("li", recursive=False):
                a_tags = li.find_all("a", recursive=False)

                if len(a_tags) <= 1:
                    continue

                # Store existing text and <a> tags in order
                new_contents = []

                for i, content in enumerate(li.contents):
                    if isinstance(content, str):
                        text = content.strip()
                        if text:
                            new_contents.append(text + " ")
                    elif content.name == "a":
                        new_contents.append(content)
                        if i < len(li.contents) - 1:
                            new_contents.append(soup.new_tag("br"))

                # Clear and re-add without wrapping in <p>
                li.clear()
                for item in new_contents:
                    li.append(item)
    except Exception as e:
        logger.critical(f"error in normalizing the ol {e}")        
    return soup


def validate_jsonld_schema(schema_str: str) -> Tuple[bool, Optional[str]]:
    """
    Validate the generated JSON-LD schema against schema.org format.
    :param schema_str: JSON-LD schema string to validate
    :return: Tuple[bool, Optional[str]]: (is_valid, error_message)
    """
    try:
        # Parse the schema string
        schema_data = json.loads(schema_str)

        # Basic structure validation
        errors = []
        
        # Check if it's a valid JSON-LD structure
        if not isinstance(schema_data, dict):
            errors.append("Schema must be a JSON object")
            return False, "; ".join(errors)
        
        # Check for required @context
        if "@context" not in schema_data:
            errors.append("Missing required '@context' property")
        elif schema_data["@context"] != "https://schema.org":
            errors.append("@context must be 'https://schema.org'")
        
        # Validate @graph structure if present
        if "@graph" in schema_data:
            if not isinstance(schema_data["@graph"], list):
                errors.append("@graph must be an array")
            else:
                for i, node in enumerate(schema_data["@graph"]):
                    node_errors = validate_schema_node(node, i, SCHEMA_ORG_CONTEXT)
                    errors.extend(node_errors)
        else:
            # Single node validation
            node_errors = validate_schema_node(schema_data, 0, SCHEMA_ORG_CONTEXT)
            errors.extend(node_errors)
        
        if errors:
            return False, "; ".join(errors)
        
        return True, None
        
    except json.JSONDecodeError as e:
        return False, f"Invalid JSON format: {str(e)}"

    except Exception as e:
        logger.critical(f"Error validating schema: {e}")
        return False, f"Validation error: {str(e)}"


def validate_schema_node(node: Dict, node_index: int, schema_context: Dict) -> List[str]:
    """
    Validate individual schema node against schema.org types and properties.
    :param node: Individual schema node to validate.
    :param node_index: Index of the node for error reporting.
    :param schema_context: Schema context containing valid types and properties.
    :return: List of validation errors.
    """
    errors = []
    
    # Check for required @type
    if "@type" not in node:
        errors.append(f"Node {node_index}: Missing required '@type' property")
        return errors
    
    node_type = node["@type"]
    
    # Validate @type exists in schema.org
    if node_type not in schema_context.get("@graph", []):
        # Check if it's a valid schema.org type
        valid_types = [item.get("@id", "").replace("schema:", "") 
                    for item in schema_context.get("@graph", []) 
                    if item.get("@type") == "rdfs:Class"]
        
        if node_type not in valid_types:
            errors.append(f"Node {node_index}: Invalid @type '{node_type}' not found in schema.org")
    
    # Validate properties for this type
    for prop, _ in node.items():
        if prop.startswith("@"):
            continue  # Skip JSON-LD keywords
            
        # Check if property is valid for schema.org
        valid_properties = [item.get("@id", "").replace("schema:", "") 
                        for item in schema_context.get("@graph", []) 
                        if item.get("@type") == "rdf:Property"]
        
        if prop not in valid_properties:
            errors.append(f"Node {node_index}: Invalid property '{prop}' not found in schema.org")
    
    return errors
